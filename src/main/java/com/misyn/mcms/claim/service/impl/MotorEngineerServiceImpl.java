package com.misyn.mcms.claim.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.misyn.mcms.claim.controller.callcenter.validator.AssessorLoggerTrail;
import com.misyn.mcms.claim.dao.*;
import com.misyn.mcms.claim.dao.impl.*;
import com.misyn.mcms.claim.dao.impl.motorengineer.*;
import com.misyn.mcms.claim.dao.motorengineer.*;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.CallCenterCommonBasketDto;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;
import com.misyn.mcms.claim.enums.*;
import com.misyn.mcms.claim.exception.*;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Email;
import com.misyn.mcms.utility.Parameters;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import static com.misyn.mcms.utility.AppConstant.*;

public class MotorEngineerServiceImpl extends AbstractBaseService<MotorEngineerServiceImpl> implements MotorEngineerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MotorEngineerServiceImpl.class);

    private final InspectionDao inspectionDao = new InspectionDaoImpl();
    private final MotorEngineerDetailsDao motorEngineerDetailsDao = new MotorEngineerDetailsDaoImpl();
    private final InspectionDetailsDao inspectionDetailsDao = new InspectionDetailsDaoImpl();
    private final AssessorAllocationDao assessorAllocationDao = new AssessorAllocationDaoImpl();
    private final TireCondtionDao tireCondtionDao = new TireCondtionDaoImpl();
    private final TireCondtionMeDao tireCondtionMeDao = new TireCondtionMeDaoImpl();
    private final SpecialRemarkDao specialRemarkDao = new SpecialRemarkDaoImpl();
    private final DesktopInspectionDetailsMeDao desktopInspectionDetailsMeDao = new DesktopInspectionDetailsMeDaoImpl();
    private final DesktopInspectionDetailsDao desktopInspectionDetailsDao = new DesktopInspectionDetailsDaoImpl();
    private final GarageInspectionDetailsMeDao garageInspectionDetailsMeDao = new GarageInspectionDetailsMeDaoImpl();
    private final GarageInspectionDetailsDao garageInspectionDetailsDao = new GarageInspectionDetailsDaoImpl();
    private final DrSupplementaryInspectionDetailsMeDao drSupplementaryInspectionDetailsMeDao = new DrSupplementaryInspectionDetailsMeDaoImpl();
    private final DrSupplementaryInspectionDetailsDao drSupplementaryInspectionDetailsDao = new DrSupplementaryInspectionDetailsDaoImpl();
    private final OnSiteInspectionDetailsMeDao onSiteInspectionDetailsMeDao = new OnSiteInspectionDetailsMeDaoImpl();
    private final OnSiteInspectionDetailsDao onSiteInspectionDetailsDao = new OnSiteInspectionDetailsDaoImpl();
    private final ARIInspectionDetailsMeDao aRIInspectionDetailsMeDao = new ARIInspectionDetailsMeDaoImpl();
    private final ARIInspectionDetailsDao aRIInspectionDetailsDao = new ARIInspectionDetailsDaoImpl();
    private final ClaimDocumentTypeDao claimDocumentTypeDao = new ClaimDocumentTypeDaoImpl();
    private final ClaimDocumentDao claimDocumentDao = new ClaimDocumentDaoImpl();
    private final ThirdPartyAssessorMeDao thirdPartyAssessorDao = new ThirdPartyAssessorMeDaoImpl();
    private final LoggerTrailDao loggerTrailDao = new LoggerTrailDaoImpl();
    private final ClaimImageDao claimImageDao = new ClaimImageDaoImpl();
    private final ClaimHandlerDao claimHandlerDao = new ClaimHandlerDaoImpl();
    private final NotificationDao notificationDao = new NotificationDaoImpl();
    private final AssessorDao assessorDao = new AssessorDaoImpl();
    private final ClaimUserAllocationService claimUserAllocationService = new ClaimUserAllocationServiceImpl();
    private final InspectionDetailsService inspectionDetailsService = new InspectionDetailsServiceImpl();
    private final AssessorPaymentDetailsDao assessorPaymentDetailsDao = new AssessorPaymentDetailsDaoImpl();
    private final CallCenterDao callCenterDao = new CallCenterDaoImpl();
    private final RequestAriDao requestAriDao = new RequestAriDaoImpl();
    private final RequestAriService requestAriService = new RequestAriServiceImpl();
    private final CallCenterService callCenterService = new CallCenterServiceImpl();
    private final EmailService emailService = new EmailServiceImpl();
    private final ClaimWiseDocumentDao claimWiseDocumentDao = new ClaimWiseDocumentDaoImpl();
    private final AssessorAllocationService assessorAllocationService = new AssessorAllocationServiceImpl(inspectionDetailsService);
    private final McmsClaimOfflineReserveAssessorDao offlineReserveAssessorDao = new McmsClaimOfflineReserveAssessorDaoImpl();
    private final McmsClaimOfflineReserveClaimDao offlineReserveClaimDao = new McmsClaimOfflineReserveClaimDaoImpl();
    private final ClaimCalculationSheetMainDao claimCalculationSheetMainDao = new ClaimCalculationSheetMainDaoImpl();
    private final ClaimCalculationSheetMainTempDao claimCalculationSheetMainTempDao = new ClaimCalculationSheetMainTempDaoImpl();
    private final ChangeRequestDetailDao changeRequestDetailDao = new ChangeRequestDetailsDaoImpl();
    private final ClaimPanelAssignUserDao claimPanelAssignUserDao = new ClaimPanelAssignUserDaoImpl();
    private final UserAuthorityLimitDao userAuthorityLimitDao = new UserAuthorityLimitDaoImpl();
    private final AssessorPaymentDeductionDetailDao assessorPaymentDeductionDetailDao = new AssessorPaymentDeductionDetailDaoImpl();
    private final ApproveAssessorPaymentClaimWiseDao approveAssessorPaymentClaimWiseDao = new ApproveAssessorPaymentClaimWiseDaoImpl();
    private final RtePendingClaimDetailDao rtePendingClaimDetailDao = new RtePendingClaimDetailDaoImpl();
    private final SupplyOrderSummaryDao supplyOrderSummaryDao = new SupplyOrderSummaryDaoImpl();
    private final PolicyDao policyDao = new PolicyDaoImpl();
    private final AuthAssignRteDao authAssignRteDao = new AuthAssignRteDaoImpl();
    private final ClaimUserAllocationDao claimUserAllocationDao = new ClaimUserAllocationDaoImpl();
    private final UserDao userDao = new UserDaoImpl();
    private final LargeClaimEmailDao largeClaimEmailDao = new LargeClaimEmailDaoImpl();
    private final EmailDao emailDao = new EmailDaoImpl();
    private final InvestigationDetailsDao investigationDetailsDao = new InvestigationDetailsDaoImpl();
    private final RestPolicyDetailsService restPolicyDetailsService = new RestPolicyDetailsServiceImpl();
    private final CallCenterCommonBasketDao commonBasketDao = new CallCenterCommonBasketDaoImpl();
    private final DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private final CalculationProcessFlowDao calculationProcessFlowDao = new CalculationProcessFlowDaoImpl();
    private final ClaimCalculationSheetTypeDao claimCalculationSheetTypeDao = new ClaimCalculationSheetTypeDaoImpl();
    private final ClaimCalculationSheetPayeeDao claimCalculationSheetPayeeDao = new ClaimCalculationSheetPayeeDaoImpl();
    private final ClaimCalculationSheetDetailDao claimCalculationSheetDetailDao = new ClaimCalculationSheetDetailDaoImpl();
    private final ClaimPaymentDispatchDao claimPaymentDispatchDao = new ClaimPaymentDispatchDaoImpl();
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final HttpClient client = HttpClient.newHttpClient();
    private static final String TOKEN_CLIENT_ID = Parameters.getTokenClientId();
    private static final String TOKEN_CLIENT_SECRET = Parameters.getTokenClientSecret();
    private static final String TOKEN_CONTENT_TYPE = Parameters.getTokenContentType();
    private static final String TOKEN_USERNAME = Parameters.getTokenUsername();
    private static final String TOKEN_PASSWORD = Parameters.getTokenPassword();
    private static final String TOKEN_URL = Parameters.getTokenUrl();
    private static final String TOKEN_GRANT_TYPE = Parameters.getTokenGrantType();
    private static final String SAVE_ENDPOINT_URL = Parameters.getSaveEndpointUrl();
    private static final String SAVE_ENDPOINT_CONTENT_TYPE = Parameters.getSaveEndpointContentType();
    private static final String ADMIN_ENDPOINT_URL = Parameters.getAdminEndpointUrl();
    /**
     * A regex to extract token from the response ("access_token":"<value>") : Sahan_N
     */
    private static Matcher accessTokenExtractionRegex(String responseBody) {
        Pattern pattern = Pattern.compile(TOKEN_EXTRACTION_REGEX);
        return pattern.matcher(responseBody);
    }

    @Override
    public MotorEngineerDetailsDto insert(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) throws Exception {
        return null;
    }

    private void saveInspectionReportDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {
        motorEngineerDetailsDao.saveInspectionReportDetails(connection, motorEngineerDetailsDto);
    }

    private void saveAssessorFeeInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {
        motorEngineerDetailsDao.saveAssessorFeeInspectionDetails(connection, motorEngineerDetailsDto);
    }

    @Override
    public MotorEngineerDetailsDto update(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) throws Exception {
        return null;
    }

    private void garageInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user, boolean isForward, boolean isChangeRequest) throws Exception {
        boolean isInsert = false;
        motorEngineerDetailsDto.getGarageInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
        try {
            GarageInspectionDetailsDto oldGarage = garageInspectionDetailsMeDao.searchMaster(connection, motorEngineerDetailsDto.getRefNo());
            if (null == oldGarage) {
                isInsert = true;
                oldGarage = motorEngineerDetailsDto.getGarageInspectionDetailsDto();
                oldGarage.setOldAcr(motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr());
            } else {
                oldGarage.setExcess(BigDecimal.valueOf(0.00));
            }

            if (AppConstant.AUTH_INSPECTION_DETAILS.equalsIgnoreCase(motorEngineerDetailsDto.getActionType())) {
                updateGarageAuthInspectionDetails(motorEngineerDetailsDto, connection, oldGarage, user, isInsert, isForward, isChangeRequest);
            } else if (AppConstant.AUTH_ASSESSOR_FEE.equalsIgnoreCase(motorEngineerDetailsDto.getActionType())) {
                updateGarageAuthAssessorFeeDetails(motorEngineerDetailsDto, connection, oldGarage, isInsert);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateGarageAuthInspectionDetails(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection, GarageInspectionDetailsDto oldGarage, UserDto user, boolean isInsert, boolean isForward, boolean isChangeRequest) throws Exception {
        AssessorLoggerTrail<GarageInspectionDetailsDto> loggerGarageTrail = new AssessorLoggerTrail<>();
        try {
            if (isForward && oldGarage.getAcr().equals(oldGarage.getOldAcr())) {
                motorEngineerDetailsDto.getGarageInspectionDetailsDto().setOldAcr(oldGarage.getAcr());
            } else if (isForward) {
                motorEngineerDetailsDto.getGarageInspectionDetailsDto().setOldAcr(oldGarage.getOldAcr());
            } else {
                motorEngineerDetailsDto.getGarageInspectionDetailsDto().setOldAcr(motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr());
            }
            if (isInsert) {
                garageInspectionDetailsMeDao.insertGarageInspectionDetailMaster(connection, motorEngineerDetailsDto.getGarageInspectionDetailsDto());
            } else {
                garageInspectionDetailsMeDao.updateGarageInspectionDetailMaster(connection, motorEngineerDetailsDto.getGarageInspectionDetailsDto());
            }

            List<ClaimLogTrailDto> loggerGarageTrailList = loggerGarageTrail.getLoggerTrailDetailsList(motorEngineerDetailsDto.getGarageInspectionDetailsDto(), oldGarage, AppConstant.MOTOR_ENGINEER_MODULE_LOG);
            loggerTrailDao.insertLoggerTrailList(connection, loggerGarageTrailList, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getInputUserId(), 1);

            changeAriRequestDetails(connection, motorEngineerDetailsDto, motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAriAndSalvage().getCondtionType(), user, isChangeRequest);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateGarageAuthAssessorFeeDetails(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection, GarageInspectionDetailsDto oldGarage, boolean isInsert) throws Exception {
        AssessorLoggerTrail<GarageInspectionDetailsDto> loggerGarageTrail = new AssessorLoggerTrail<>();
        try {
            if (isInsert) {
                garageInspectionDetailsMeDao.insertGarageAssessorFeeDetailMaster(connection, motorEngineerDetailsDto.getGarageInspectionDetailsDto());
            } else {
                garageInspectionDetailsMeDao.updateGarageAssessorFeeDetailMaster(connection, motorEngineerDetailsDto.getGarageInspectionDetailsDto());
            }
            List<ClaimLogTrailDto> loggerGarageTrailList = loggerGarageTrail.getLoggerTrailDetailsList(motorEngineerDetailsDto.getGarageInspectionDetailsDto(), oldGarage, AppConstant.MOTOR_ENGINEER_ASSESSOR_PAYMENT_MODULE_LOG);
            loggerTrailDao.insertLoggerTrailList(connection, loggerGarageTrailList, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getInputUserId(), 1);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void drSupplementaryInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user, boolean isForward, boolean isChangeRequest) throws Exception {
        boolean isInsert = false;
        motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
        try {
            DrSupplementaryInspectionDetailsDto oldDr = drSupplementaryInspectionDetailsMeDao.searchMaster(connection, motorEngineerDetailsDto.getRefNo());
            if (null == oldDr) {
                isInsert = true;
                oldDr = motorEngineerDetailsDto.getDrSuppInspectionDetailsDto();
                oldDr.setOldAcr(motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAcr());
            }
            if (AppConstant.AUTH_INSPECTION_DETAILS.equalsIgnoreCase(motorEngineerDetailsDto.getActionType())) {
                updateDrSupplementaryAuthInspectionDetails(motorEngineerDetailsDto, connection, oldDr, user, isInsert, isForward, isChangeRequest);
            } else if (AppConstant.AUTH_ASSESSOR_FEE.equalsIgnoreCase(motorEngineerDetailsDto.getActionType())) {
                updateDrSupplementaryAuthAssessorFeeDetails(motorEngineerDetailsDto, connection, oldDr, isInsert);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateDrSupplementaryAuthInspectionDetails(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection, DrSupplementaryInspectionDetailsDto oldDr, UserDto user, boolean isInsert, boolean isForward, boolean isChangeRequest) throws Exception {
        AssessorLoggerTrail<DrSupplementaryInspectionDetailsDto> loggerDrTrail = new AssessorLoggerTrail<>();
        try {
            if (isForward && oldDr.getAcr().equals(oldDr.getOldAcr())) {
                motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().setOldAcr(oldDr.getAcr());
            } else if (isForward) {
                motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().setOldAcr(oldDr.getOldAcr());
            } else {
                motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().setOldAcr(motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAcr());
            }
            if (isInsert) {
                drSupplementaryInspectionDetailsMeDao.insertDrInspectionDetailMaster(connection, motorEngineerDetailsDto.getDrSuppInspectionDetailsDto());
            } else {
                drSupplementaryInspectionDetailsMeDao.updateDrInspectionDetailMaster(connection, motorEngineerDetailsDto.getDrSuppInspectionDetailsDto());
            }

            List<ClaimLogTrailDto> loggerDrTrailDetailsList = loggerDrTrail.getLoggerTrailDetailsList(motorEngineerDetailsDto.getDrSuppInspectionDetailsDto(), oldDr, AppConstant.MOTOR_ENGINEER_MODULE_LOG);
            loggerTrailDao.insertLoggerTrailList(connection, loggerDrTrailDetailsList, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getInputUserId(), 1);

            changeAriRequestDetails(connection, motorEngineerDetailsDto, motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAriAndSalvage().getCondtionType(), user, isChangeRequest);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }

    }

    private void updateDrSupplementaryAuthAssessorFeeDetails(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection, DrSupplementaryInspectionDetailsDto oldDr, boolean isInsert) throws Exception {
        AssessorLoggerTrail<DrSupplementaryInspectionDetailsDto> loggerDrTrail = new AssessorLoggerTrail<>();
        try {
            if (isInsert) {
                drSupplementaryInspectionDetailsMeDao.insertDrAssessorFeeDetailMaster(connection, motorEngineerDetailsDto.getDrSuppInspectionDetailsDto());
            } else {
                drSupplementaryInspectionDetailsMeDao.updateDrAssessorFeeDetailMaster(connection, motorEngineerDetailsDto.getDrSuppInspectionDetailsDto());
            }
            List<ClaimLogTrailDto> loggerDrTrailList = loggerDrTrail.getLoggerTrailDetailsList(motorEngineerDetailsDto.getDrSuppInspectionDetailsDto(), oldDr, AppConstant.MOTOR_ENGINEER_ASSESSOR_PAYMENT_MODULE_LOG);
            loggerTrailDao.insertLoggerTrailList(connection, loggerDrTrailList, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getInputUserId(), 1);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void ariSalvageInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) throws Exception {
        boolean isInsert = false;
        motorEngineerDetailsDto.getAriInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
        try {
            ARIInspectionDetailsDto oldAri = aRIInspectionDetailsMeDao.searchMaster(connection, motorEngineerDetailsDto.getRefNo());
            if (null == oldAri) {
                isInsert = true;
                oldAri = motorEngineerDetailsDto.getAriInspectionDetailsDto();
            }
            if (AppConstant.AUTH_INSPECTION_DETAILS.equalsIgnoreCase(motorEngineerDetailsDto.getActionType())) {
                updateAriSalvageAuthInspectionDetails(motorEngineerDetailsDto, connection, oldAri, user, isInsert);
            } else if (AppConstant.AUTH_ASSESSOR_FEE.equalsIgnoreCase(motorEngineerDetailsDto.getActionType())) {
                updateAriSalvageAuthAssessorFeeDetails(motorEngineerDetailsDto, connection, oldAri, isInsert);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateAriSalvageAuthInspectionDetails(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection, ARIInspectionDetailsDto oldAri, UserDto user, boolean isInsert) throws Exception {
        AssessorLoggerTrail<ARIInspectionDetailsDto> loggerAriTrail = new AssessorLoggerTrail<>();
        try {
            if (isInsert) {
                aRIInspectionDetailsMeDao.insertAriInspectionDetailMaster(connection, motorEngineerDetailsDto.getAriInspectionDetailsDto());
            } else {
                aRIInspectionDetailsMeDao.updateAriInspectionDetailMaster(connection, motorEngineerDetailsDto.getAriInspectionDetailsDto());
            }

            List<ClaimLogTrailDto> loggerArList = loggerAriTrail.getLoggerTrailDetailsList(motorEngineerDetailsDto.getAriInspectionDetailsDto(), oldAri, AppConstant.MOTOR_ENGINEER_MODULE_LOG);
            loggerTrailDao.insertLoggerTrailList(connection, loggerArList, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getInputUserId(), 1);
            requestAriDao.updatePartiallyReview(connection, AppConstant.YES.equalsIgnoreCase(motorEngineerDetailsDto.getAriInspectionDetailsDto().getPartiallyReview()), motorEngineerDetailsDto.getRefNo());

            if (!motorEngineerDetailsDao.isAriSalvagePending(connection, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo())) {
                ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, motorEngineerDetailsDto.getClaimNo());
                String assignUserId = claimHandlerDto.getAssignUserId();
                if (claimHandlerDto.getClaimStatus().equals(AppConstant.CLAIM_STATUS_LIABILITY_APPROVED)) {
                    ClaimCalculationSheetMainDto activeCalsheet = claimCalculationSheetMainDao.getLatestCalsheet(connection, motorEngineerDetailsDto.getClaimNo());
                    if (null == activeCalsheet) {
                        if (null != claimHandlerDto.getSupplyOrderAssignStatus() && claimHandlerDto.getSupplyOrderAssignStatus().isEmpty() && !claimHandlerDto.getSupplyOrderAssignStatus().equals(AppConstant.NO)) {
                            SupplyOrderSummaryDto ongoingSupplyOrder = supplyOrderSummaryDao.getOngoingSupplyOrder(connection, motorEngineerDetailsDto.getClaimNo());
                            if (ongoingSupplyOrder.getSupplyOrderStatus().equals("SCRUTINIZING-F")) {
                                assignUserId = ongoingSupplyOrder.getApprvAssignScrutinizingUserId();
                            } else if (ongoingSupplyOrder.getSupplyOrderStatus().equals("SCRUTINIZING-A")) {
                                assignUserId = ongoingSupplyOrder.getApprvScrutinizingUserId();
                            } else {
                                assignUserId = ongoingSupplyOrder.getApproveAssignSparePartCoordinator();
                            }
                        }
                    } else {
                        if (activeCalsheet.getStatus().equals(AppConstant.CLAIM_STATUS_FORWARDED_TO_THE_SPARE_PARTS_COORDINATOR_FOR_THE_CREATION_OF_CALCULATION_SHEET)) {
                            assignUserId = activeCalsheet.getSparePartCordinatorAssignUserId();
                        } else if (activeCalsheet.getStatus().equals(AppConstant.CLAIM_STATUS_FORWARDED_TO_THE_BILL_CHECK_TEAM_FOR_THE_RECOMMENDATION_OF_THE_CALCULATION_SHEET)) {
                            assignUserId = activeCalsheet.getScrutinizeTeamAssignUserId();
                        }
                    }
                }

                //When claim goes from d maker side and assignUserId null in claimHandler table notification user Changed to D Maker
                //because user is null
                if (null == assignUserId) {
                    assignUserId = claimHandlerDto.getDecisionMakingAssignUserId();
                }

                saveNotification(connection, motorEngineerDetailsDto.getClaimNo(), user.getUserId(), assignUserId, motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionValue().concat(AppConstant.YES.equalsIgnoreCase(motorEngineerDetailsDto.getAriInspectionDetailsDto().getPartiallyReview()) ? " Partially Reviewed" : " Fully Reviewed"), AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(motorEngineerDetailsDto.getClaimNo())));

                claimHandlerDao.updateStoreStatus(connection, motorEngineerDetailsDto.getClaimNo(), "N");
                saveClaimsLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, "Auto Re-Store File", "Claim File Auto Re-Stored due to Pending ARI/ Salvage Inspection Approval");
                saveClaimProcessFlow(connection, motorEngineerDetailsDto.getClaimNo(), 0, "Auto Re-Store File", "SYSTEM", Utility.sysDateTime(), AppConstant.STRING_EMPTY, AppConstant.YES);

            }
            String arrangeUser = motorEngineerDetailsDto.getInspectionDetailsDto().getAssessorAllocationDto().getInputUserId();
            Integer accessUserType = userDao.getAccessUserTypeByUserId(connection, arrangeUser);
            if (AppConstant.TECHNICAL_CORDINATOR_ACCESSUSRTYPE.equals(accessUserType) || AppConstant.TECHNICAL_CORDINATOR_ARI_ACCESSUSRTYPE.equals(accessUserType)) {
                List<String> technicalCoordinators = userDao.getTechnicalCodinators(connection);
                for (String technicalCoordinator : technicalCoordinators) {
                    saveNotification(connection, motorEngineerDetailsDto.getClaimNo(), user.getUserId(), technicalCoordinator, motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionValue().concat(AppConstant.YES.equalsIgnoreCase(motorEngineerDetailsDto.getAriInspectionDetailsDto().getPartiallyReview()) ? " Partially Reviewed" : " Fully Reviewed"), "/MotorEngineerController/viewBillCheck".concat("?P_N_CLIM_NO=").concat(Integer.toString(motorEngineerDetailsDto.getClaimNo())));
                }
            }
            if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, motorEngineerDetailsDto.getClaimNo())) {
                sendNotificationOnAuthorizedInspections(connection, motorEngineerDetailsDto.getClaimNo(), user, true, motorEngineerDetailsDto.getInspectionDetailsDto().getInspectionId());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }

    }

    private void updateAriSalvageAuthAssessorFeeDetails(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection, ARIInspectionDetailsDto oldAri, boolean isInsert) throws Exception {
        AssessorLoggerTrail<ARIInspectionDetailsDto> loggerAriTrail = new AssessorLoggerTrail<>();
        try {
            if (isInsert) {
                aRIInspectionDetailsMeDao.insertAriAssessorFeeDetailMaster(connection, motorEngineerDetailsDto.getAriInspectionDetailsDto());
            } else {
                aRIInspectionDetailsMeDao.updateAriAssessorFeeDetailMaster(connection, motorEngineerDetailsDto.getAriInspectionDetailsDto());
            }

            List<ClaimLogTrailDto> loggerArList = loggerAriTrail.getLoggerTrailDetailsList(motorEngineerDetailsDto.getAriInspectionDetailsDto(), oldAri, AppConstant.MOTOR_ENGINEER_ASSESSOR_PAYMENT_MODULE_LOG);
            loggerTrailDao.insertLoggerTrailList(connection, loggerArList, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getInputUserId(), 1);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void desktopInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user, boolean isForward, boolean isChangeRequest) throws Exception {
        AssessorLoggerTrail<DesktopInspectionDetailsDto> loggerTrail = new AssessorLoggerTrail<>();
        motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
        try {
            DesktopInspectionDetailsDto oldDesktop = desktopInspectionDetailsMeDao.searchMaster(connection, motorEngineerDetailsDto.getRefNo());
            if (null != oldDesktop) {
                if (isForward && oldDesktop.getAcr().equals(oldDesktop.getOldAcr())) {
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setOldAcr(oldDesktop.getAcr());
                } else if (isForward) {
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setOldAcr(oldDesktop.getOldAcr());
                } else {
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setOldAcr(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAcr());
                }
            } else {
                motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setOldAcr(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAcr());
            }
            if (null == oldDesktop) {
                desktopInspectionDetailsMeDao.insertMaster(connection, motorEngineerDetailsDto.getDesktopInspectionDetailsDto());
                oldDesktop = motorEngineerDetailsDto.getDesktopInspectionDetailsDto();
            } else {
                desktopInspectionDetailsMeDao.updateMaster(connection, motorEngineerDetailsDto.getDesktopInspectionDetailsDto());
            }

            List<ClaimLogTrailDto> loggerTrailList = loggerTrail.getLoggerTrailDetailsList(motorEngineerDetailsDto.getDesktopInspectionDetailsDto(), oldDesktop, AppConstant.MOTOR_ENGINEER_MODULE_LOG);
            loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getInputUserId(), 1);

            changeAriRequestDetails(connection, motorEngineerDetailsDto, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAriSalvage().getCondtionType(), user, isChangeRequest);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void onsiteInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user, boolean isForward, boolean isChangeRequest) throws Exception {
        boolean isInsert = false;
        motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
        try {
            OnSiteInspectionDetailsDto oldOnSite = onSiteInspectionDetailsMeDao.searchMaster(connection, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
            if (null == oldOnSite) {
                isInsert = true;
                oldOnSite = motorEngineerDetailsDto.getOnSiteInspectionDetailsDto();
                oldOnSite.setOldAcr(motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr());
            }
            if (AppConstant.AUTH_INSPECTION_DETAILS.equalsIgnoreCase(motorEngineerDetailsDto.getActionType())) {
                updateOnsiteAuthInspectionDetails(motorEngineerDetailsDto, connection, oldOnSite, user, isInsert, isForward, isChangeRequest);
                claimHandlerDao.updateOnsiteOffer(connection, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getOfferType());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateOnsiteAuthInspectionDetails(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection, OnSiteInspectionDetailsDto oldOnsite, UserDto user, boolean isInsert, boolean isForward, boolean isChangeRequest) throws Exception {
        AssessorLoggerTrail<OnSiteInspectionDetailsDto> loggerOnSiteTrail = new AssessorLoggerTrail<>();

        if (AppConstant.PROVIDE_OFFER == motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getOfferType()) {
            motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().setProvideOffer(ConditionType.Yes);
        } else {
            motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().setProvideOffer(ConditionType.No);
        }

        if (isForward && oldOnsite.getAcr().equals(oldOnsite.getOldAcr())) {
            motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().setOldAcr(oldOnsite.getAcr());
        } else if (isForward) {
            motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().setOldAcr(oldOnsite.getOldAcr());
        } else {
            motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().setOldAcr(motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr());
        }
        try {
            if (isInsert) {
                onSiteInspectionDetailsMeDao.insertMaster(connection, motorEngineerDetailsDto.getOnSiteInspectionDetailsDto());
            } else {
                onSiteInspectionDetailsMeDao.updateMaster(connection, motorEngineerDetailsDto.getOnSiteInspectionDetailsDto());
            }

            List<ClaimLogTrailDto> loggerOnList = loggerOnSiteTrail.getLoggerTrailDetailsList(motorEngineerDetailsDto.getOnSiteInspectionDetailsDto(), oldOnsite, AppConstant.MOTOR_ENGINEER_MODULE_LOG);
            loggerTrailDao.insertLoggerTrailList(connection, loggerOnList, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getInputUserId(), 1);

            if (!inspectionDetailsDao.isApprovedInspectionNotInOnsite(connection, motorEngineerDetailsDto.getClaimNo())) {
                changeAriRequestDetails(connection, motorEngineerDetailsDto, motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getRequestAri().getCondtionType(), user, isChangeRequest);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }

    }

    private void changeAriRequestDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, String isAri, UserDto user, boolean isChangeRequest) throws Exception {
        try {
            boolean isAriRequested = requestAriDao.searchByClaimNoisAriRequested(connection, motorEngineerDetailsDto.getClaimNo());
            if (AppConstant.YES.equalsIgnoreCase(isAri) && !isAriRequested) {
                saveAri(connection, motorEngineerDetailsDto, user);
                // Removed notification sending when rte requested the ari : #4602215
//                List<User> userList = requestAriDao.getUserListByAccessUserType(connection, AppConstant.TECHNICAL_CORDINATOR_ACCESSUSRTYPE);
//                List<User> userListAriOnly = requestAriDao.getUserListByAccessUserType(connection, AppConstant.TECHNICAL_CORDINATOR_ARI_ACCESSUSRTYPE);
//                userList.addAll(userListAriOnly);
//                for (UserDto userDto : userList) {
//                    if (! user.getUserId().equals(userDto.getUserId())) {
//                        saveNotfication(connection, motorEngineerDetailsDto.getClaimNo(), user.getUserId(), userDto.getUserId(), "ARI Requested for the Claim", "/RequestAriController/ariListView?TYPE="
//                                .concat(user.getN_accessusrtype() == AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM || user.getN_accessusrtype() == AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR ? "6" : "5").concat("&P_N_CLIM_NO=").concat(Integer.toString(motorEngineerDetailsDto.getClaimNo())));
//                    }
//                }
//                rtePendingClaimDetailDao.savePendingJobs(connection, motorEngineerDetailsDto.getClaimNo(), user.getN_accessusrtype(), user.getUserId());
            } else if (AppConstant.NO.equalsIgnoreCase(isAri) && isChangeRequest) {
                requestAriDao.updateStatusByClaimNoAndStatus(connection, AppConstant.DELETE, motorEngineerDetailsDto.getClaimNo(), AppConstant.STRING_PENDING, AppConstant.STRING_UPLOAD);
                saveClaimsLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, "Cancel ARI Request", "canceled ARI request from " + user.getUserId());
//                rtePendingClaimDetailDao.removePendingJobs(connection, motorEngineerDetailsDto.getClaimNo());
            }
//            else if ((AppConstant.NO.equalsIgnoreCase(isAri) && isAriRequested)) {
//                Cancel ARI status = 'D' - DELETE
//                requestAriDao.updateStatusByClaimNoAndStatus(connection, AppConstant.DELETE, motorEngineerDetailsDto.getClaimNo(), AppConstant.STRING_PENDING);
//                saveClaimsLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, "Cancel ARI Request", "canceled ARI request from " + user.getUserId());
//            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public MotorEngineerDetailsDto delete(MotorEngineerDetailsDto inspectionDetailsDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public MotorEngineerDetailsDto updateAuthPending(MotorEngineerDetailsDto inspectionDetailsDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public MotorEngineerDetailsDto deleteAuthPending(MotorEngineerDetailsDto inspectionDetailsDto, UserDto user) throws Exception {
        return null;
    }

    @Override
    public MotorEngineerDetailsDto auth(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public MotorEngineerDetailsDto reject(Object id, UserDto user) throws Exception {
        return null;
    }

    @Override
    public MotorEngineerDetailsDto reject(Object id, UserDto user, String rejectMessage) throws Exception {
        return null;
    }

    @Override
    public MotorEngineerDetailsDto search(Object id) throws Exception {
        Connection connection = null;
        MotorEngineerDetailsDto motorEngineerDetailsDto = null;
        try {
            connection = getJDBCConnection();
            motorEngineerDetailsDto = this.search(connection, id);
            if (0 != motorEngineerDetailsDto.getClaimNo()) {
                motorEngineerDetailsDto.setTotalApproveAcrAmount(claimHandlerDao.getTotalAcrAmount(connection, motorEngineerDetailsDto.getClaimNo()));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return motorEngineerDetailsDto;
    }

    @Override
    public MotorEngineerDetailsDto searchAuthPending(Object id) throws Exception {
        return null;
    }

    @Override
    public List<MotorEngineerDetailsDto> searchAll() throws Exception {
        return null;
    }

    @Override
    public List<MotorEngineerDetailsDto> searchAllAuthPending() throws Exception {
        return null;
    }

    @Override
    public String getMessage(int messageId) {
        return null;
    }

    @Override
    public DataGridDto getClaimDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) throws Exception {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = motorEngineerDetailsDao.getJobDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, fromDate, toDate);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public List<SpecialRemarkDto> searchRemarksByClaimNo(Integer claimNo, Integer departmentId) throws Exception {
        Connection connection = null;
        List<SpecialRemarkDto> list = null;
        try {
            connection = getJDBCConnection();
            list = specialRemarkDao.searchRemarksByClaimNo(connection, claimNo, departmentId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    private MotorEngineerDetailsDto saveInspectionDetails(MotorEngineerDetailsDto inspectionDetailsDto, Connection connection) throws Exception {
        return motorEngineerDetailsDao.saveInspectionDetails(connection, inspectionDetailsDto);
    }

    private List<TireCondtionDto> saveTyreCondition(List<TireCondtionDto> tireCondtionDtoList, Connection connection) throws Exception {
        for (TireCondtionDto tireCondtionDto : tireCondtionDtoList) {
            tireCondtionMeDao.insertMaster(connection, tireCondtionDto);
        }
        return tireCondtionDtoList;
    }

    private void updateInspectionReportDetails(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection) throws Exception {
        AssessorLoggerTrail<MotorEngineerDetailsDto> loggerTrail = new AssessorLoggerTrail<>();
        try {
            MotorEngineerDetailsDto oldInspection = motorEngineerDetailsDao.searchMaster(connection, motorEngineerDetailsDto.getRefNo());
            List<ClaimLogTrailDto> loggerTrailList = loggerTrail.getLoggerTrailDetailsList(motorEngineerDetailsDto, oldInspection, AppConstant.MOTOR_ENGINEER_INSPECTION_REPORT_MODULE_LOG);
            loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getInputUserId(), 1);

            motorEngineerDetailsDao.updateInspectionReportDetails(connection, motorEngineerDetailsDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateInspectionDetails(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection) throws Exception {
        AssessorLoggerTrail<MotorEngineerDetailsDto> loggerTrail = new AssessorLoggerTrail<>();
        try {
            MotorEngineerDetailsDto oldInspection = motorEngineerDetailsDao.searchMaster(connection, motorEngineerDetailsDto.getRefNo());

            List<ClaimLogTrailDto> loggerTrailInspectionDetailList = loggerTrail.getLoggerTrailDetailsList(motorEngineerDetailsDto, oldInspection, AppConstant.MOTOR_ENGINEER_MODULE_LOG);
            loggerTrailDao.insertLoggerTrailList(connection, loggerTrailInspectionDetailList, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getInputUserId(), 1);

            List<ClaimLogTrailDto> loggerTrailInspectionReportList = loggerTrail.getLoggerTrailDetailsList(motorEngineerDetailsDto, oldInspection, AppConstant.MOTOR_ENGINEER_INSPECTION_REPORT_MODULE_LOG);
            loggerTrailDao.insertLoggerTrailList(connection, loggerTrailInspectionReportList, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getInputUserId(), 1);


            motorEngineerDetailsDao.updateInspectionDetails(connection, motorEngineerDetailsDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateAssessorFeeInspectionDetails(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection) throws Exception {
        AssessorLoggerTrail<MotorEngineerDetailsDto> loggerTrail = new AssessorLoggerTrail<>();
        try {
            MotorEngineerDetailsDto oldInspection = motorEngineerDetailsDao.searchMaster(connection, motorEngineerDetailsDto.getRefNo());
            List<ClaimLogTrailDto> loggerTrailList = loggerTrail.getLoggerTrailDetailsList(motorEngineerDetailsDto, oldInspection, AppConstant.MOTOR_ENGINEER_ASSESSOR_PAYMENT_MODULE_LOG);
            loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getInputUserId(), 1);

            motorEngineerDetailsDao.updateAssessorFeeInspectionDetails(connection, motorEngineerDetailsDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private List<TireCondtionDto> updateTyreCondition(List<TireCondtionDto> tireCondtionDtoList, Connection connection, String userId, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {
        int index = 0;
        List<ClaimLogTrailDto> list = new ArrayList<>();
        int claimNo;
        for (TireCondtionDto tireCondtionDto : tireCondtionDtoList) {
            TireCondtionDto oldTireCondtionDto = tireCondtionMeDao.searchTireCondtionByClaimNoAndPosition(connection, tireCondtionDto);
            if (null == oldTireCondtionDto) {
                tireCondtionMeDao.insertMaster(connection, tireCondtionDto);
            } else {
                claimNo = tireCondtionDto.getClaimsDto().getClaimNo();
                List<ClaimLogTrailDto> claimLogTrailDtos = saveTireCondtionLog(tireCondtionDto, oldTireCondtionDto, index, claimNo, userId);
                if (null != claimLogTrailDtos && claimLogTrailDtos.size() > 0) {
                    loggerTrailDao.insertLoggerTrailList(connection, claimLogTrailDtos, claimNo, motorEngineerDetailsDto.getRefNo(), userId, 1);
                }
                index++;
                tireCondtionMeDao.updateMaster(connection, tireCondtionDto);
            }
        }
        return tireCondtionDtoList;
    }

    private boolean saveSpecialRemark(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user, Connection connection) {
        SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
        specialRemarkDto.setInputDateTime(Utility.sysDateTime());
        specialRemarkDto.setInputUserId(user.getUserId());
        specialRemarkDto.setClaimNo(motorEngineerDetailsDto.getClaimNo());
        try {
            if (null != motorEngineerDetailsDto.getAssessorAllocationDto() && null != motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto()) {
                if (!motorEngineerDetailsDto.getInspectionSpecialRemark().isEmpty()) {
                    String inspectionValue = AppConstant.STRING_EMPTY;
                    if (null != motorEngineerDetailsDto.getInspectionDto() && 0 != motorEngineerDetailsDto.getInspectionDto().getInspectionId()) {
                        InspectionDto inspectionDto = inspectionDao.searchMaster(connection, motorEngineerDetailsDto.getInspectionDto().getInspectionId());
                        inspectionValue = inspectionDto.getInspectionValue();

                    }


                    specialRemarkDto.setDepartmentId(AppConstant.MOTOR_ENGINEER_MODULE);
                    specialRemarkDto.setSectionName(AppConstant.MOTOR_ENGINEER_MODULE_SECTION.concat("-").concat(inspectionValue));
                    specialRemarkDto.setRemark(motorEngineerDetailsDto.getInspectionSpecialRemark());
                    specialRemarkDao.insertMaster(connection, specialRemarkDto);
                    specialRemarkDto.setRemark(AppConstant.STRING_EMPTY);
                    return true;
                }

                int inspectionId = motorEngineerDetailsDto.getInspectionDto().getInspectionId();


                if (inspectionId == AppConstant.GARAGE_INSPECTION) {
                    if (null != motorEngineerDetailsDto.getGarageInspectionDetailsDto() && !motorEngineerDetailsDto.getGarageInspectionDetailsDto().getSpecialRemark().isEmpty()) {
                        specialRemarkDto.setRemark(motorEngineerDetailsDto.getGarageInspectionDetailsDto().getSpecialRemark());
                    }
                } else if (inspectionId == AppConstant.DESKTOP_INSPECTION) {
                    if (null != motorEngineerDetailsDto.getDesktopInspectionDetailsDto() && !motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getSpecialRemark().isEmpty()) {
                        specialRemarkDto.setRemark(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getSpecialRemark());
                    }
                } else if (inspectionId == AppConstant.DR_INSPECTION || inspectionId == AppConstant.SUP_INSPECTION) {
                    if (null != motorEngineerDetailsDto.getDrSuppInspectionDetailsDto() && !motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getSpecialRemark().isEmpty()) {
                        specialRemarkDto.setRemark(motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getSpecialRemark());
                    }
                } else if (inspectionId == AppConstant.ARI_INSPECTION || inspectionId == AppConstant.SALVAGE_INSPECTION) {
                    if (null != motorEngineerDetailsDto.getAriInspectionDetailsDto() && !motorEngineerDetailsDto.getAriInspectionDetailsDto().getSpecialRemark().isEmpty()) {
                        specialRemarkDto.setRemark(motorEngineerDetailsDto.getAriInspectionDetailsDto().getSpecialRemark());
                    }
                } else {
                    if (null != motorEngineerDetailsDto.getOnSiteInspectionDetailsDto() && !motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getSpecialRemark().isEmpty()) {
                        specialRemarkDto.setRemark(motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getSpecialRemark());
                    }
                }
                if (null != specialRemarkDto.getRemark() && !specialRemarkDto.getRemark().isEmpty()) {
                    InspectionDto inspectionDto = inspectionDao.searchMaster(connection, inspectionId);
                    specialRemarkDto.setDepartmentId(AppConstant.MOTOR_ENGINEER_MODULE);
                    specialRemarkDto.setSectionName(AppConstant.MOTOR_ENGINEER_MODULE_SECTION.concat("-").concat(inspectionDto.getInspectionValue()));
                    specialRemarkDao.insertMaster(connection, specialRemarkDto);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }

        return false;
    }

    @Override
    public ClaimInspectionTypeDto getInspectionTypeDto(String id) throws Exception {
        Connection connection = null;
        ClaimInspectionTypeDto claimInspectionTypeDto = null;
        try {
            connection = getJDBCConnection();
            claimInspectionTypeDto = inspectionDao.getInspectionTypeDto(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimInspectionTypeDto;
    }

    @Override
    public List<ClaimDocumentTypeDto> getClaimDocumentTypeDtoList(Integer departmentId, Integer inspectionTypeId) {
        List<ClaimDocumentTypeDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = claimDocumentTypeDao.getClaimDocumentTypeDtoList(connection, departmentId, inspectionTypeId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<ClaimDocumentDto> getClaimDocumentDtoList(Integer jobRefNo, Integer departmentId) {
        List<ClaimDocumentDto> list = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = claimDocumentDao.getClaimDocumentDtoList(connection, jobRefNo, departmentId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<ClaimThirdPartyDetailsGenericDto> getClaimThirdPartyDetailsGeneric(Integer claimNo) throws Exception {
        Connection connection = null;
        List<ClaimThirdPartyDetailsGenericDto> thirdPartyDtos = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            thirdPartyDtos = thirdPartyAssessorDao.searchAll(connection, claimNo);
            for (ClaimThirdPartyDetailsGenericDto thirdPartyDto : thirdPartyDtos) {
                thirdPartyDto.setType("MOTOR_ENGINEER");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return thirdPartyDtos;
    }

    @Override
    public List<ClaimUploadViewDto> getClaimUploadViewDtoList(Integer claimNo, Integer jobRefNo, Integer departmentId, Integer inspectionTypeId) {
        Connection connection = null;
        List<ClaimUploadViewDto> list = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            list = claimDocumentDao.getClaimUploadViewDtoList(connection, claimNo, jobRefNo, departmentId, inspectionTypeId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<PreviousClaimsDto> getPreviousClaimList(Integer polNo, Integer jobNo) throws Exception {
        Connection connection = null;
        List<PreviousClaimsDto> list = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            List<Integer> claimList = motorEngineerDetailsDao.getClaimListByPolNo(polNo, connection);
            for (Integer claimNo : claimList) {
                List<PreviousClaimsDto> previousClaimList = motorEngineerDetailsDao.getPreviousClaimList(claimNo, jobNo, connection);
                if (previousClaimList.size() > 0) {
                    PreviousClaimsDto previousClaimsDto = new PreviousClaimsDto();
                    previousClaimsDto.setClaimNo(claimNo);
                    previousClaimsDto.setList(previousClaimList);
                    list.add(previousClaimsDto);
                }

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;

    }

    @Override
    public List<PreviousClaimsDto> getPreviousInspectionClaimList(Integer claimNo, Integer jobNo) throws Exception {
        Connection connection = null;
        List<PreviousClaimsDto> list = new ArrayList<>();
        try {
            connection = getJDBCConnection();

            List<PreviousClaimsDto> previousClaimList = motorEngineerDetailsDao.getPreviousClaimList(claimNo, jobNo, connection);
            if (previousClaimList.size() > 0) {
                PreviousClaimsDto previousClaimsDto = new PreviousClaimsDto();
                previousClaimsDto.setClaimNo(claimNo);
                previousClaimsDto.setList(previousClaimList);
                list.add(previousClaimsDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public List<ClaimImageDto> getClaimImageDtoList(Integer claimNo, Integer jobRefNo) {
        List<ClaimImageDto> list = new ArrayList<>();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            list = claimImageDao.getClaimImageDtoList(connection, claimNo, jobRefNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    private void saveThirdPartyAssessorDetails(MotorEngineerDetailsDto inspectionDetailsDto, UserDto user, Connection connection) throws Exception {
        Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = inspectionDetailsDto.getThirdPartyAssessorMap();
        for (Map.Entry<String, ClaimThirdPartyDetailsGenericDto> entry : thirdPartyAssessorMap.entrySet()) {
            String key = entry.getKey();
            ClaimThirdPartyDetailsGenericDto value = entry.getValue();
            if ("NEW".equals(value.getStatus())) {
                thirdPartyAssessorDao.insertMaster(connection, value);
            } else if ("EDIT".equals(value.getStatus())) {
                thirdPartyAssessorDao.updateMaster(connection, value);
            }

        }
    }

    private void updateThirdPartyAssessorDetailsStatusToEditMode(MotorEngineerDetailsDto inspectionDetailsDto) throws Exception {
        Map<String, ClaimThirdPartyDetailsGenericDto> thirdPartyAssessorMap = inspectionDetailsDto.getThirdPartyAssessorMap();
        for (Map.Entry<String, ClaimThirdPartyDetailsGenericDto> entry : thirdPartyAssessorMap.entrySet()) {
            String key = entry.getKey();
            ClaimThirdPartyDetailsGenericDto value = entry.getValue();
            if ("NEW".equals(value.getStatus())) {
                value.setStatus("EDIT");
            }
        }
    }

    private List<ClaimLogTrailDto> saveTireCondtionLog(TireCondtionDto newValue, TireCondtionDto oldValue, int index, Integer claimNo, String inputUserId) {
        String[] postions = {"Condition", "Size", "Make", "New/RB"};

        List<ClaimLogTrailDto> list = new ArrayList<>();
        boolean isChanged = false;

        if (null == oldValue) {
            oldValue = new TireCondtionDto();
        }

        String postionValue = postions[index];
        if (!newValue.getLf().equals(oldValue.getLf())) {
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(postionValue.concat(" LF"));
            claimLogTrailDto.setFieldValue(newValue.getLf());
            geClaimsDto(claimLogTrailDto, newValue, inputUserId, claimNo);
            list.add(claimLogTrailDto);

        }

        if (!newValue.getRf().equals(oldValue.getRf()) && !newValue.getRf().isEmpty()) {
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(postionValue.concat(" RF"));
            claimLogTrailDto.setFieldValue(newValue.getRf());
            geClaimsDto(claimLogTrailDto, newValue, inputUserId, claimNo);
            list.add(claimLogTrailDto);
        }

        if (!newValue.getRr().equals(oldValue.getRr()) && !newValue.getRr().isEmpty()) {
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(postionValue.concat(" RR"));
            claimLogTrailDto.setFieldValue(newValue.getRr());
            geClaimsDto(claimLogTrailDto, newValue, inputUserId, claimNo);
            list.add(claimLogTrailDto);
        }
        if (!newValue.getRl().equals(oldValue.getRl()) && !newValue.getRl().isEmpty()) {
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(postionValue.concat(" RL"));
            claimLogTrailDto.setFieldValue(newValue.getRl());
            geClaimsDto(claimLogTrailDto, newValue, inputUserId, claimNo);
            list.add(claimLogTrailDto);
        }

        if (!newValue.getRri().equals(oldValue.getRri()) && !newValue.getRri().isEmpty()) {
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(postionValue.concat(" RRI"));
            claimLogTrailDto.setFieldValue(newValue.getRri());
            geClaimsDto(claimLogTrailDto, newValue, inputUserId, claimNo);
            list.add(claimLogTrailDto);
        }
        if (!newValue.getLri().equals(oldValue.getLri()) && !newValue.getLri().isEmpty()) {
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(postionValue.concat(" LRI"));
            claimLogTrailDto.setFieldValue(newValue.getLri());
            geClaimsDto(claimLogTrailDto, newValue, inputUserId, claimNo);
            list.add(claimLogTrailDto);
        }

        if (!newValue.getOther().equals(oldValue.getOther()) && !newValue.getOther().isEmpty()) {
            ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
            claimLogTrailDto.setFieldName(postionValue.concat(" Other"));
            claimLogTrailDto.setFieldValue(newValue.getOther());
            geClaimsDto(claimLogTrailDto, newValue, inputUserId, claimNo);
            list.add(claimLogTrailDto);
        }

        return list;

    }

    private void geClaimsDto(ClaimLogTrailDto claimLogTrailDto, TireCondtionDto newValue, String inputUserId, Integer claimNo) {
        claimLogTrailDto.setClaimNo(claimNo);
        claimLogTrailDto.setInputDateTime(Utility.sysDateTime());
        claimLogTrailDto.setUserId(inputUserId);
        claimLogTrailDto.setFormNameId(AppConstant.MOTOR_ENGINEER_MODULE_LOG);

    }

    @Override
    public void forwardToInformDesktop(MotorEngineerDetailsDto motorEngineerDetailsDto, String forwardUserName, UserDto sessionUser) throws Exception {
        Connection connection = null;
        String URL = AppConstant.MOTORENG_VIEW.concat("?P_N_REF_NO=").concat(Integer.toString(motorEngineerDetailsDto.getRefNo()));
        StringBuilder sbMessage = new StringBuilder();
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            inspectionDetailsDao.updateForwardTcDesktopUser(connection, motorEngineerDetailsDto.getRefNo(), forwardUserName, Utility.sysDateTime());
            //Set and Updated Assessor allocation Record Status as 33 --> FORWARD FOR INFORM DESKTOP
            assessorAllocationDao.updateRecordStatusByRefNo(connection, 33, motorEngineerDetailsDto.getRefNo());
            //Set and Updated Assessor allocation Record Status as 33 --> FORWARD FOR INFORM DESKTOP
            inspectionDetailsDao.updateRecordStatusByRefNo(connection, 33, motorEngineerDetailsDto.getRefNo());

//            String userName = assessorAllocationDao.getInputUser(connection, motorEngineerDetailsDto.getRefNo());
            sbMessage.append("You have received a new Desktop Assessment Review");
            saveNotification(connection, motorEngineerDetailsDto.getClaimNo(), sessionUser.getUserId(), forwardUserName, sbMessage.toString(), URL);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void informDesktop(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto sessionUser) throws Exception {
        Connection connection = null;

        StringBuilder sbMessage = new StringBuilder();
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            String user = "";

            motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setInformedUser(sessionUser.getUserId());
            motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setInformedDateTime(Utility.sysDateTime());

            ClaimHandlerDto searchedClaimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, motorEngineerDetailsDto.getClaimNo());

            if (("Disagree").equals(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getIsAgreeGarage()) || ("Disagree").equals(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getIsAgreeCustomer())) {
                //Set and Updated Assessor allocation Record Status as 0 --> new claim
                assessorAllocationDao.updateRecordStatusByRefNo(connection, motorEngineerDetailsDto.getRecordStatus(), motorEngineerDetailsDto.getRefNo());
                //Set and Updated Assessor allocation Record Status as 0 --> new claim
//                inspectionDetailsDao.updateRecordStatusByRefNo(connection, 0, motorEngineerDetailsDto.getRefNo());

                inspectionDetailsDao.updateRecordStatusAndAuthStatusByRefNo(connection, motorEngineerDetailsDto.getRecordStatus(), motorEngineerDetailsDto.getRefNo(), "P");

                motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setIsInformed("No");
                motorEngineerDetailsDao.updateDesktopInformDetails(connection, motorEngineerDetailsDto);

                String URL = AppConstant.MOTORENG_VIEW.concat("?P_N_REF_NO=").concat(Integer.toString(motorEngineerDetailsDto.getRefNo()));
                String userName = assessorAllocationDao.getRTE(connection, motorEngineerDetailsDto.getRefNo());
                if (!sessionUser.getUserId().equals(userName)) {
                    if (null == searchedClaimHandlerDto.getInitLiabilityAssignUserId()) {
                        sbMessage.append("You have received a Informed Desktop Assessment Review");
                        saveNotification(connection, motorEngineerDetailsDto.getClaimNo(), sessionUser.getUserId(), userName, sbMessage.toString(), URL);
                    }
                }
            } else {
                motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setIsInformed("YES");
                motorEngineerDetailsDao.updateDesktopInformDetails(connection, motorEngineerDetailsDto);
                //Set and Updated Assessor allocation Record Status as 9 --> APPROVED
                assessorAllocationDao.updateRecordStatusByRefNo(connection, motorEngineerDetailsDto.getRecordStatus(), motorEngineerDetailsDto.getRefNo());
                //Set and Updated Assessor allocation Record Status as 9 --> APPROVED
                inspectionDetailsDao.updateRecordStatusByRefNo(connection, motorEngineerDetailsDto.getRecordStatus(), motorEngineerDetailsDto.getRefNo());

                if (searchedClaimHandlerDto.getAssignUserId() != "") {
                    user = searchedClaimHandlerDto.getAssignUserId();
                } else if (searchedClaimHandlerDto.getInitLiabilityAssignUserId() != "") {
                    user = searchedClaimHandlerDto.getInitLiabilityAssignUserId();

                }
                if (null == searchedClaimHandlerDto.getInitLiabilityAssignUserId()) {
                    sbMessage.append("You have received a Agreed Desktop Assessment");
                    String URL = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(motorEngineerDetailsDto.getClaimNo())).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
                    saveNotification(connection, motorEngineerDetailsDto.getClaimNo(), sessionUser.getUserId(), user, sbMessage.toString(), URL);
                }

            }

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void recallDesktop(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto sessionUser) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
//            inspectionDetailsDao.updateForwardTcDesktopUser(connection, motorEngineerDetailsDto.getRefNo(), forwardUserName, Utility.sysDateTime());
            //Set and Updated Assessor allocation Record Status as 9 --> APPROVED
            assessorAllocationDao.updateRecordStatusByRefNo(connection, 9, motorEngineerDetailsDto.getRefNo());
            //Set and Updated Assessor allocation Record Status as 9 --> APPROVED
            inspectionDetailsDao.updateRecordStatusByRefNo(connection, 9, motorEngineerDetailsDto.getRefNo());
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void returnDesktop(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto sessionUser) throws Exception {
        Connection connection = null;
        String URL = AppConstant.CLAIM_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(motorEngineerDetailsDto.getClaimNo())).concat("&P_TAB_INDEX=7").concat("&TYPE=2");
        StringBuilder sbMessage = new StringBuilder();
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
//            inspectionDetailsDao.updateForwardTcDesktopUser(connection, motorEngineerDetailsDto.getRefNo(), forwardUserName, Utility.sysDateTime());
            //Set and Updated Assessor allocation Record Status as 34 --> RETURN DESKTOP INSPECTION
            Integer reasonId = 26; //Rejected Desktop
            assessorAllocationDao.rejectJobStatusByJobId(connection, ClaimStatus.REJECTED.getClaimStatus(), reasonId, motorEngineerDetailsDto.getRefNo());
            assessorAllocationDao.updateRecordStatusByJobId(connection, ClaimStatus.REJECTED.getClaimStatus(), motorEngineerDetailsDto.getRefNo());
            if (rtePendingClaimDetailDao.checkIfRteJobsPending(connection, motorEngineerDetailsDto.getClaimNo())) {
                sendNotificationOnAuthorizedInspections(connection, motorEngineerDetailsDto.getClaimNo(), sessionUser, false, AppConstant.ZERO_INT);
            }
            //Set and Updated Assessor allocation Record Status as 34 --> RETURN DESKTOP INSPECTION
            inspectionDetailsDao.updateRecordStatusByRefNo(connection, ClaimStatus.REJECTED.getClaimStatus(), motorEngineerDetailsDto.getRefNo());
            String userName = assessorAllocationDao.getInputUser(connection, motorEngineerDetailsDto.getRefNo());
            sbMessage.append("You have received a Rejected Desktop Assessment - Job No : " + motorEngineerDetailsDto.getJobId());
            saveNotification(connection, motorEngineerDetailsDto.getClaimNo(), sessionUser.getUserId(), userName, sbMessage.toString(), URL);
            initialLog(motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo(), connection, sessionUser, "Desktop Inspection Rejected", "Desktop Inspection Job No : " + motorEngineerDetailsDto.getJobId() + " has been Rejected by Engineer");

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void insertDesktopInitialRecords(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            MotorEngineerDetailsDto motorEngineerDetailsDtoInit = new MotorEngineerDetailsDto();

            //Set Audit Details
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setInputDatetime(Utility.sysDateTime());
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setInputUserId(AppConstant.STRING_EMPTY);

            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setAssignRteUser(motorEngineerDetailsDto.getAssessorAllocationDto().getRteCode());
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setAssignRteDatetime(Utility.sysDateTime());
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setApproveAssignRteUser(motorEngineerDetailsDto.getAssessorAllocationDto().getRteCode());
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setApproverAssignRteDateTime(Utility.sysDateTime());

            //Set Nested Objects Values To Most Outer Object
            motorEngineerDetailsDtoInit.setRefNo(motorEngineerDetailsDto.getRefNo());
            motorEngineerDetailsDtoInit.setJobId(motorEngineerDetailsDto.getJobId());
            motorEngineerDetailsDtoInit.setClaimNo(motorEngineerDetailsDto.getClaimNo());

            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getRefNo());
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setJobId(motorEngineerDetailsDto.getJobId());
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setClaimNo(motorEngineerDetailsDto.getClaimNo());

            motorEngineerDetailsDtoInit.getInspectionDto().setInspectionId(motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId());
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().getInspectionDto().setInspectionId(motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId());

            motorEngineerDetailsDtoInit.getDesktopInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getRefNo());
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().getDesktopInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getRefNo());

            motorEngineerDetailsDtoInit.setAssessorAllocationDto(motorEngineerDetailsDto.getAssessorAllocationDto());
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setAssessorAllocationDto(motorEngineerDetailsDto.getAssessorAllocationDto());

            motorEngineerDetailsDtoInit.setTireCondtionDtoList(motorEngineerDetailsDto.getTireCondtionDtoList());
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setTireCondtionDtoList(motorEngineerDetailsDto.getTireCondtionDtoList());
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setChassisNoConfirm(SelectionType.Pending);
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setRecordStatus(motorEngineerDetailsDto.getAssessorAllocationDto().getRecordStatus());
            motorEngineerDetailsDtoInit.getInspectionDetailsDto().setAssessorFeeDetailId(motorEngineerDetailsDto.getAssessorFeeDetailId());

            inspectionDetailsDao.insertMaster(connection, motorEngineerDetailsDtoInit.getInspectionDetailsDto());
            desktopInspectionDetailsDao.insertMaster(connection, motorEngineerDetailsDtoInit.getInspectionDetailsDto().getDesktopInspectionDetailsDto());

            for (TireCondtionDto tireCondtionDto : motorEngineerDetailsDtoInit.getTireCondtionDtoList()) {
                tireCondtionDao.insertMaster(connection, tireCondtionDto);
//                tireCondtionMeDao.insertMaster(connection, tireCondtionDto);
            }

//            motorEngineerDetailsDao.insertMaster(connection, motorEngineerDetailsDtoInit);
//            desktopInspectionDetailsMeDao.insertMaster(connection, motorEngineerDetailsDtoInit.getDesktopInspectionDetailsDto());

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public boolean updateToChangeRequest(Integer refNo, String remark, UserDto user, boolean isRteChange) throws Exception {
        return updateToChangeRequest(refNo, remark, user, isRteChange, AppConstant.STRING_EMPTY, AppConstant.ZERO_INT);
    }

    @Override
    public boolean updateToChangeRequest(Integer refNo, String remark, UserDto user, boolean isRteChange, String approveAssignRteUser, Integer claimNo) throws Exception {
        Connection connection = null;
        boolean isUpdated = false;

        try {
            connection = getJDBCConnection();
            InspectionDetailsDto inspectionDetailsDto = inspectionDetailsDao.searchMaster(connection, refNo);

//            isCheckValidChangeRequest(connection, inspectionDetailsDto.getRecordStatus(), inspectionDetailsDto.getClaimNo());

            AssessorAllocationDto assessorAllocationDto = assessorAllocationDao.searchMaster(connection, refNo);

            boolean isInitLiabilityAssignUserChangeRequest = claimHandlerDao.isCheckInitLiabilityAssignUserChangeRequest(connection, user.getUserId(), inspectionDetailsDto.getClaimNo());

            if (isInitLiabilityAssignUserChangeRequest) {
                return false;
            }

            ChangeRequestDetailDto changeRequestDetailDto = changeRequestDetailDao.searchByRefNo(connection, refNo);

            if (null == changeRequestDetailDto) {
                changeRequestDetailDto = new ChangeRequestDetailDto();
                changeRequestDetailDto.setRefNo(refNo);
                changeRequestDetailDto.setClaimNo(inspectionDetailsDto.getClaimNo());
                changeRequestDetailDto.setAssignRte(approveAssignRteUser.isEmpty() ? inspectionDetailsDto.getInspectionDetailsAuthUserId() : approveAssignRteUser);
                changeRequestDetailDto.setRequestUser(user.getUserId());
                changeRequestDetailDto.setRequestDatetime(Utility.sysDateTime());
                changeRequestDetailDto.setResponseType(AppConstant.STRING_PENDING);

                changeRequestDetailDao.insertMaster(connection, changeRequestDetailDto);
            } else {
                changeRequestDetailDto.setAssignRte(approveAssignRteUser.isEmpty() ? inspectionDetailsDto.getAssignRteUser() : approveAssignRteUser);
                changeRequestDetailDto.setRequestUser(user.getUserId());
                changeRequestDetailDto.setRequestDatetime(Utility.sysDateTime());
                changeRequestDetailDto.setResponseUser(null);
                changeRequestDetailDto.setResponseDatetime(null);
                changeRequestDetailDto.setResponseType(AppConstant.STRING_PENDING);

                changeRequestDetailDao.update(connection, changeRequestDetailDto);
            }

            if (null != assessorAllocationDto) {
                inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
            }

            List<String> assignUserbyClaimNo = getAssignUserbyClaimNo(connection, assessorAllocationDto.getClaimsDto().getClaimNo());
            for (String assignUser : assignUserbyClaimNo) {
                int accessUserTypeByUserId = userDao.getAccessUserTypeByUserId(connection, assignUser);
                if (accessUserTypeByUserId == AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM || accessUserTypeByUserId == AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR) {
                    rtePendingClaimDetailDao.savePendingJobs(connection, assessorAllocationDto.getClaimsDto().getClaimNo(), accessUserTypeByUserId, assignUser);
                    break;
                }
            }

            isUpdated = inspectionDetailsDao.updateRecordStatusByRefNo(connection, ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus(), refNo);

            if (isUpdated) {
                isUpdated = assessorAllocationDao.updateRecordStatusByJobId(connection, ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus(), refNo);
                if (isUpdated) {
                    MotorEngineerDetailsDto motorEngineerDetailsDto = this.search(connection, refNo);
                    if (null != motorEngineerDetailsDto) {
                        BigDecimal acrValue = getAcrValue(motorEngineerDetailsDto);
//                        isUpdated = claimHandlerDao.updateReserveAmount(acrValue, acrValue, connection, assessorAllocationDto.getClaimsDto().getClaimNo());
                        if (isUpdated) {
                            MotorEngineerDetailsDto motorEngineerDetails = motorEngineerDetailsDto;
                            InspectionDto inspectionDto = inspectionDetailsDto.getInspectionDto();
                            motorEngineerDetails.setInspectionDto(inspectionDto);
                            motorEngineerDetails.setAssessorAllocationDto(assessorAllocationDto);
                            inspectionDetailsToDefault(connection, motorEngineerDetails, user);

                        }
                    }
                    if (isRteChange) {
                        inspectionDetailsDao.updateForwardDetails(connection, approveAssignRteUser, approveAssignRteUser.isEmpty() ? Utility.getDate(AppConstant.DEFAULT_DATE_TIME, AppConstant.DATE_TIME_FORMAT) : Utility.sysDateTime(), refNo);
                        inspectionDetailsDto.setAssignRteUser(approveAssignRteUser.isEmpty() ? inspectionDetailsDto.getAssignRteUser() : approveAssignRteUser);
                    }
                    saveSpecialRemarkToChangeRequest(user, remark, inspectionDetailsDto.getClaimNo(), connection, isRteChange);
                    saveNotification(user, inspectionDetailsDto, connection, isRteChange);
                    initialLog(inspectionDetailsDto, connection, user, "Change Request", "Change request requested");
                    this.saveClaimsLogs(connection, inspectionDetailsDto.getClaimNo(), user, "Change Request [Job No - " + assessorAllocationDto.getJobId() + "]", "Change request requested [" + inspectionDetailsDto.getAssignRteUser() + "]");
                    shiftNotificationOnAction(connection, claimNo, user);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return isUpdated;
    }

    private void isCheckValidChangeRequest(Connection connection, int recordStatus, int claimNo) throws Exception {
        try {
            if (ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus() == recordStatus) {
                throw new ErrorMsgException(AppConstant.ALREADY_SEND_CHANGE_REQUEST, "already requested to change request");
            }
            if (claimCalculationSheetMainDao.isPaymentForwardedToApprovalOrApprove(connection, claimNo)) {
                throw new ErrorMsgException(AppConstant.PAYMENT_APPROVE_OR_FORWARDED_TO_APPROVAL, "Can not be Request to Change Request,Payment Forwarded to Approval or Voucher Pending ");
            }
        } catch (ErrorMsgException e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public boolean addRteRemarks(int refNo, String rteRemarks) throws Exception {
        Connection connection = null;
        boolean updateRemarks = false;
        try {
            connection = getJDBCConnection();
            updateRemarks = inspectionDetailsDao.addRteRemarks(connection, refNo, rteRemarks);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return updateRemarks;
    }

    @Override
    public boolean addSpecialRemark(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) throws Exception {
        Connection connection = null;
        boolean updateRemarks = false;
        try {
            int claimNo = motorEngineerDetailsDto.getClaimNo();
            connection = getJDBCConnection();
            if (motorEngineerDetailsDto.getClaimNo() != 0) {
                updateRemarks = saveSpecialRemark(motorEngineerDetailsDto, user, connection);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return updateRemarks;
    }

    @Override
    public List<PreviousClaimsDto> getInspectionList(Connection connection, Integer claimNo) {
        List<PreviousClaimsDto> previousClaimsDtoList = new ArrayList<>();
        try {
            previousClaimsDtoList = motorEngineerDetailsDao.searchAllByClaimNo(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return previousClaimsDtoList;
    }

    @Override
    public MotorEngineerDetailsDto search(Connection connection, Object id) {
        MotorEngineerDetailsDto motorEngineerDetailsDto = null;
        try {

            AssessorAllocationDto assessorAllocationDto = assessorAllocationDao.searchMaster(connection, id);

            boolean isApprovedInspectionReportDetails = motorEngineerDetailsDao.isApprovedInspectionReportDetails(connection, id);
            boolean isApprovedOrPendingInspectionDetails = motorEngineerDetailsDao.isApprovedOrPendingInspectionDetails(connection, id) || (null != assessorAllocationDto && ClaimStatus.INSPECTION_FORWARDED.getClaimStatus() == assessorAllocationDto.getRecordStatus());
            boolean isApprovedAssessorFeeInspectionDetails = motorEngineerDetailsDao.isApprovedAssessorFeeInspectionDetails(connection, id);

//            motorEngineerDetailsDto = motorEngineerDetailsDao.searchMaster(connection, id);
            motorEngineerDetailsDto = getMotorEngineerDetails(connection, id, isApprovedInspectionReportDetails, isApprovedOrPendingInspectionDetails, isApprovedAssessorFeeInspectionDetails);
            if (null == motorEngineerDetailsDto) {
                motorEngineerDetailsDto = new MotorEngineerDetailsDto();
//                if (null != assessorAllocationDto) {
//                    List<ClaimLogTrailDto> loggerTrailList = loggerTrailDao.getLoggerTrailForForm(connection, AppConstant.ASSESSOR_AND_MOTOR_LOG, assessorAllocationDto.getClaimsDto().getClaimNo(), assessorAllocationDto.getRefNo());
//                    motorEngineerDetailsDto.setLogList(loggerTrailList);
//                }
                return motorEngineerDetailsDto;
            }

            motorEngineerDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
            if (null != motorEngineerDetailsDto.getInspectionDto()) {
                if (motorEngineerDetailsDto.getInspectionDto().getInspectionId() == AppConstant.GARAGE_INSPECTION) {
                    GarageInspectionDetailsDto garageInspectionDetailsDto = getGarageDetails(connection, id, isApprovedOrPendingInspectionDetails, isApprovedAssessorFeeInspectionDetails);
                    if (null != garageInspectionDetailsDto) {
                        motorEngineerDetailsDto.setGarageInspectionDetailsDto(garageInspectionDetailsDto);
                        motorEngineerDetailsDto.setCurrentApprovedAdvanceAmount(isApprovedOrPendingInspectionDetails ? motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAdvancedAmount() : BigDecimal.ZERO);
                    }
                } else if (motorEngineerDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DR_INSPECTION || motorEngineerDetailsDto.getInspectionDto().getInspectionId() == AppConstant.SUP_INSPECTION) {
                    DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto = getDrSupplementaryInspectionDetails(connection, id, isApprovedOrPendingInspectionDetails, isApprovedAssessorFeeInspectionDetails);
                    if (null != drSupplementaryInspectionDetailsDto) {
                        motorEngineerDetailsDto.setDrSuppInspectionDetailsDto(drSupplementaryInspectionDetailsDto);
                    }
                } else if (motorEngineerDetailsDto.getInspectionDto().getInspectionId() == AppConstant.ARI_INSPECTION || motorEngineerDetailsDto.getInspectionDto().getInspectionId() == AppConstant.SALVAGE_INSPECTION) {
                    ARIInspectionDetailsDto ariInspectionDetailsDto = getARIInspectionDetails(connection, id, isApprovedOrPendingInspectionDetails, isApprovedAssessorFeeInspectionDetails);
//                            aRIInspectionDetailsMeDao.searchMaster(connection, id);
                    if (null != ariInspectionDetailsDto) {
                        motorEngineerDetailsDto.setAriInspectionDetailsDto(ariInspectionDetailsDto);
                    }
                } else if (motorEngineerDetailsDto.getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION) {
                    DesktopInspectionDetailsDto desktopInspectionDetailsDto = desktopInspectionDetailsMeDao.searchMaster(connection, id);
                    if (null != desktopInspectionDetailsDto) {
                        motorEngineerDetailsDto.setDesktopInspectionDetailsDto(desktopInspectionDetailsDto);
                        motorEngineerDetailsDto.setCurrentApprovedAdvanceAmount(isApprovedOrPendingInspectionDetails ? motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAdvancedAmount() : BigDecimal.ZERO);
                    }
                } else {
                    OnSiteInspectionDetailsDto onSiteInspectionDetailsDto = getOnSiteInspectionDetails(connection, id, isApprovedOrPendingInspectionDetails);
                    if (null != onSiteInspectionDetailsDto) {
                        motorEngineerDetailsDto.setOnSiteInspectionDetailsDto(onSiteInspectionDetailsDto);
                    }
                }
//                if (null != assessorAllocationDto) {
//                    List<ClaimLogTrailDto> loggerTrailList = loggerTrailDao.getLoggerTrailForForm(connection, AppConstant.ASSESSOR_AND_MOTOR_LOG, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo());
//                    motorEngineerDetailsDto.setLogList(loggerTrailList);
//                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        return motorEngineerDetailsDto;
    }

    private OnSiteInspectionDetailsDto getOnSiteInspectionDetails(Connection connection, Object id, boolean isApprovedOrPendingInspectionDetails) {
        OnSiteInspectionDetailsDto onSiteInspectionDetailsDto = new OnSiteInspectionDetailsDto();
        try {
            if (isApprovedOrPendingInspectionDetails) {
                return onSiteInspectionDetailsMeDao.searchMaster(connection, id);
            }
            return onSiteInspectionDetailsDao.searchMaster(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return onSiteInspectionDetailsDto;
    }

    private ARIInspectionDetailsDto getARIInspectionDetails(Connection connection, Object id, boolean isApprovedOrPendingInspectionDetails, boolean isApprovedAssessorFeeInspectionDetails) {
        ARIInspectionDetailsDto ariInspectionDetailsDto = new ARIInspectionDetailsDto();
        try {
            if (isApprovedOrPendingInspectionDetails && isApprovedAssessorFeeInspectionDetails) {
                return aRIInspectionDetailsMeDao.searchMaster(connection, id);
            } else if (isApprovedOrPendingInspectionDetails) {
                ariInspectionDetailsDto = aRIInspectionDetailsMeDao.getARIInspectionDetails(connection, ariInspectionDetailsDto, id);
                return aRIInspectionDetailsDao.getARIInspectionAssessorFeeDetails(connection, ariInspectionDetailsDto, id);
            } else if (isApprovedAssessorFeeInspectionDetails) {
                ariInspectionDetailsDto = aRIInspectionDetailsMeDao.getARIInspectionAssessorFeeDetails(connection, ariInspectionDetailsDto, id);
                return aRIInspectionDetailsDao.getARIInspectionDetails(connection, ariInspectionDetailsDto, id);
            }
            return aRIInspectionDetailsDao.searchMaster(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return ariInspectionDetailsDto;
    }

    private DrSupplementaryInspectionDetailsDto getDrSupplementaryInspectionDetails(Connection connection, Object id, boolean isApprovedOrPendingInspectionDetails, boolean isApprovedAssessorFeeInspectionDetails) {
        DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto = new DrSupplementaryInspectionDetailsDto();
        try {
            if (isApprovedOrPendingInspectionDetails && isApprovedAssessorFeeInspectionDetails) {
                return drSupplementaryInspectionDetailsMeDao.searchMaster(connection, id);
            } else if (isApprovedOrPendingInspectionDetails) {
                drSupplementaryInspectionDetailsDto = drSupplementaryInspectionDetailsMeDao.getDrSupplementaryInspectionDetails(connection, drSupplementaryInspectionDetailsDto, id);
                return drSupplementaryInspectionDetailsDao.getDrSupplementaryInspectionAssessorFeeDetails(connection, drSupplementaryInspectionDetailsDto, id);
            } else if (isApprovedAssessorFeeInspectionDetails) {
                drSupplementaryInspectionDetailsDto = drSupplementaryInspectionDetailsMeDao.getDrSupplementaryAssessorFeeDetails(connection, drSupplementaryInspectionDetailsDto, id);
                return drSupplementaryInspectionDetailsDao.getDrSupplementaryInspectionDetails(connection, drSupplementaryInspectionDetailsDto, id);
            }
            return drSupplementaryInspectionDetailsDao.searchMaster(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return drSupplementaryInspectionDetailsDto;
    }

    private GarageInspectionDetailsDto getGarageDetails(Connection connection, Object id, boolean isApprovedOrPendingInspectionDetails, boolean isApprovedAssessorFeeInspectionDetails) {
        GarageInspectionDetailsDto garageInspectionDetailsDto = new GarageInspectionDetailsDto();
        try {
            if (isApprovedOrPendingInspectionDetails && isApprovedAssessorFeeInspectionDetails) {
                return garageInspectionDetailsMeDao.searchMaster(connection, id);
            } else if (isApprovedOrPendingInspectionDetails) {
                garageInspectionDetailsDto = garageInspectionDetailsMeDao.getGarageInspectionDetails(connection, garageInspectionDetailsDto, id);
                return garageInspectionDetailsDao.getGarageInspectionAssessorFeeDetails(connection, garageInspectionDetailsDto, id);
            } else if (isApprovedAssessorFeeInspectionDetails) {
                garageInspectionDetailsDto = garageInspectionDetailsMeDao.getGarageAssessorFeeDetails(connection, garageInspectionDetailsDto, id);
                return garageInspectionDetailsDao.getGarageInspectionDetails(connection, garageInspectionDetailsDto, id);
            }
            return garageInspectionDetailsDao.searchMaster(connection, id);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    private MotorEngineerDetailsDto getMotorEngineerDetails(Connection connection, Object id, boolean isApprovedInspectionReportDetails, boolean isApprovedOrPendingInspectionDetails, boolean isApprovedAssessorFeeInspectionDetails) {
        MotorEngineerDetailsDto motorEngineerDetailsDto = new MotorEngineerDetailsDto();
        List<TireCondtionDto> tireCondtionDtos;
        try {
            String action = AppConstant.SAVE;

            if (isApprovedOrPendingInspectionDetails) {
                motorEngineerDetailsDto = motorEngineerDetailsDao.getInspectionDetails(connection, motorEngineerDetailsDto, id);
                action = AppConstant.UPDATE;

            } else {
                if (isApprovedInspectionReportDetails) {
                    motorEngineerDetailsDto = motorEngineerDetailsDao.getInspectionReportDetails(connection, motorEngineerDetailsDto, id);
                    action = AppConstant.UPDATE;
                } else {
                    motorEngineerDetailsDto = inspectionDetailsDao.getInspectionDetails(connection, motorEngineerDetailsDto, id);
                }
            }

            if (isApprovedAssessorFeeInspectionDetails) {
                motorEngineerDetailsDto = motorEngineerDetailsDao.getAssessorFeeInspectionDetails(connection, motorEngineerDetailsDto, id);
                action = AppConstant.UPDATE;
            } else {
                motorEngineerDetailsDto = inspectionDetailsDao.getAssessorFeeInspectionDetails(connection, motorEngineerDetailsDto, id);
            }

            if (tireCondtionMeDao.isApprovedTireCondition(connection, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo())) {
                tireCondtionDtos = tireCondtionMeDao.searchByClaimNoAndRefNo(connection, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo());
            } else {
                tireCondtionDtos = tireCondtionDao.searchByClaimNoAndRefNo(connection, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo());
            }
            motorEngineerDetailsDto.setTireCondtionDtoList(tireCondtionDtos);
            motorEngineerDetailsDto.setAction(action);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return motorEngineerDetailsDto;
    }

    private MotorEngineerDetailsDto setAdvanceAmountDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, int claimNo) {
        try {
            BigDecimal paidAdvanceAmount = claimCalculationSheetMainDao.getPaidTotalAdvanceAmount(connection, claimNo);
            BigDecimal balanceAdvanceAmount = claimHandlerDao.getAdvanceAmount(connection, claimNo);
            BigDecimal currentProvision = claimHandlerDao.getReserveAmountAfterApproved(connection, claimNo);
            motorEngineerDetailsDto.setPaidAdvanceAmount(paidAdvanceAmount);
            motorEngineerDetailsDto.setBalanceAdvanceAmount(balanceAdvanceAmount);
            motorEngineerDetailsDto.setTotalApprovedAdvanceAmount(balanceAdvanceAmount.add(paidAdvanceAmount));
            motorEngineerDetailsDto.setCurrentProvision(currentProvision);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return motorEngineerDetailsDto;
    }

    @Override
    public List<MotorEngineerDetailsDto> getMotorEngineerDetailsDtoList(Integer claimNo) {
        List<MotorEngineerDetailsDto> list = new ArrayList<>();
        Connection connection = null;
        MotorEngineerDetailsDto motorEngineerDetailsDto;
        try {
            connection = getJDBCConnection();
            ClaimsDto claimsDto = callCenterService.getReportAccidentClaimsDtoByClaimNo(connection, claimNo);
            List<InspectionFormDto> inspectionFormDtoList = motorEngineerDetailsDao.getInspectionFormDtoList(connection, claimNo);
            if (null != inspectionFormDtoList && !inspectionFormDtoList.isEmpty()) {
                for (InspectionFormDto inspectionFormDto : inspectionFormDtoList) {
                    Integer refNo = inspectionFormDto.getRefNo();
                    AssessorAllocationDto assessorAllocationDto = assessorAllocationService.search(connection, refNo);
                    assessorAllocationDto.setClaimsDto(claimsDto);
                    InspectionDetailsDto inspectionDetailsDto = inspectionDetailsService.search(connection, refNo);
                    inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
                    motorEngineerDetailsDto = this.search(connection, refNo);
                    motorEngineerDetailsDto.setInspectionDetailsDto(inspectionDetailsDto);
                    list.add(motorEngineerDetailsDto);
                }
            } else {
                motorEngineerDetailsDto = new MotorEngineerDetailsDto();
                AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
                InspectionDetailsDto inspectionDetailsDto = new InspectionDetailsDto();
                assessorAllocationDto.setClaimsDto(claimsDto);
                inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
                motorEngineerDetailsDto.setInspectionDetailsDto(inspectionDetailsDto);
                list.add(motorEngineerDetailsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return list;
    }

    @Override
    public MotorEngineerDetailsDto getLatestUpdateOnSite(MotorEngineerDetailsDto motorEngineerDetailsDto) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            InspectionDetailsDto latestInspection = inspectionDetailsDao.getLatestInspection(connection, motorEngineerDetailsDto.getClaimNo());
            MotorEngineerDetailsDto latestEngineer = motorEngineerDetailsDao.getLatestInspectionMotorEngineer(connection, motorEngineerDetailsDto.getClaimNo());

            if (null != latestEngineer) {
                OnSiteInspectionDetailsDto onsite = onSiteInspectionDetailsMeDao.searchMaster(connection, latestEngineer.getRefNo());
                motorEngineerDetailsDto.setChassisNo(latestInspection.getChassisNo());
                DesktopInspectionDetailsDto desktopInspectionDetailsDto = motorEngineerDetailsDto.getDesktopInspectionDetailsDto();
                if (null != desktopInspectionDetailsDto) {
                    motorEngineerDetailsDto.setChassisNo(latestEngineer.getChassisNo());
                    motorEngineerDetailsDto.setPav(latestEngineer.getPav());
                    motorEngineerDetailsDto.setDamageDetails(latestEngineer.getDamageDetails());
                    motorEngineerDetailsDto.setPad(latestEngineer.getPad());
                    motorEngineerDetailsDto.setGenuineOfAccident(latestEngineer.getGenuineOfAccident());
                    motorEngineerDetailsDto.setInvestReq(latestEngineer.getInvestReq());
                    motorEngineerDetailsDto.getInspectionDetailsDto().setAssessorSpecialRemark(latestInspection.getAssessorSpecialRemark());
                    motorEngineerDetailsDto.setAssessorSpecialRemark(latestEngineer.getAssessorSpecialRemark());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setBoldPercent(onsite.getBoldPercent());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setBoldTyrePenaltyAmount(onsite.getBoldTirePenaltyAmount());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setUnderPenaltyPercent(onsite.getUnderPenaltyPercent());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setUnderInsurancePenaltyAmount(onsite.getUnderPenaltyAmount());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setPayableAmount(onsite.getPayableAmount());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setAcr(onsite.getAcr());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setExcess(onsite.getExcess());
                    motorEngineerDetailsDto.setEngNoConfirm(latestEngineer.getEngNoConfirm());

                    motorEngineerDetailsDto.setDesktopInspection(AppConstant.YES);

                    List<TireCondtionDto> tireCondtionDtos = tireCondtionMeDao.searchByClaimNoAndRefNo(connection, motorEngineerDetailsDto.getClaimNo(), latestEngineer.getRefNo());
                    motorEngineerDetailsDto.setTireCondtionDtoList(tireCondtionDtos);

                }
            } else if (null != latestInspection) {
                latestInspection = inspectionDetailsService.search(connection, latestInspection.getRefNo());
                motorEngineerDetailsDto.setChassisNo(latestInspection.getChassisNo());
                DesktopInspectionDetailsDto desktopInspectionDetailsDto = motorEngineerDetailsDto.getDesktopInspectionDetailsDto();
                if (null != desktopInspectionDetailsDto) {
                    motorEngineerDetailsDto.setChassisNo(latestInspection.getChassisNo());
                    motorEngineerDetailsDto.setPav(latestInspection.getPav());
                    motorEngineerDetailsDto.setDamageDetails(latestInspection.getDamageDetails());
                    motorEngineerDetailsDto.setPad(latestInspection.getPad());
                    motorEngineerDetailsDto.setGenuineOfAccident(latestInspection.getGenuineOfAccident());
                    motorEngineerDetailsDto.setInvestReq(latestInspection.getInvestReq());
                    motorEngineerDetailsDto.setEngNoConfirm(latestInspection.getEngNoConfirm());
                    motorEngineerDetailsDto.getInspectionDetailsDto().setAssessorSpecialRemark(latestInspection.getAssessorSpecialRemark());
                    motorEngineerDetailsDto.setAssessorSpecialRemark(AppConstant.STRING_EMPTY);

                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setBoldPercent(latestInspection.getOnSiteInspectionDetailsDto().getBoldPercent());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setBoldTyrePenaltyAmount(latestInspection.getOnSiteInspectionDetailsDto().getBoldTirePenaltyAmount());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setUnderPenaltyPercent(latestInspection.getOnSiteInspectionDetailsDto().getUnderPenaltyPercent());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setUnderInsurancePenaltyAmount(latestInspection.getOnSiteInspectionDetailsDto().getUnderPenaltyAmount());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setPayableAmount(latestInspection.getOnSiteInspectionDetailsDto().getPayableAmount());
                    motorEngineerDetailsDto.setDesktopInspection(AppConstant.YES);

                    List<TireCondtionDto> tireCondtionDtos = tireCondtionMeDao.searchByClaimNoAndRefNo(connection, motorEngineerDetailsDto.getClaimNo(), latestInspection.getRefNo());
                    motorEngineerDetailsDto.setTireCondtionDtoList(tireCondtionDtos);

                }
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return motorEngineerDetailsDto;
    }

    @Override
    public boolean updateAssignUser(Integer refNo, String assignUser, UserDto user, Integer ClaimNo) throws MisynJDBCException {
        String URL = AppConstant.MOTORENG_VIEW.concat("?P_N_REF_NO=").concat(Integer.toString(refNo));
        StringBuilder sbMessage = new StringBuilder();
        Connection connection = getJDBCConnection();
        boolean success;
        String assignedUser;
        try {
            beginTransaction(connection);
            InspectionDetailsDto inspectionDetailsDto = inspectionDetailsDao.searchMaster(connection, refNo);
            if (inspectionDetailsDto.getRecordStatus() == ClaimStatus.INSPECTION_FORWARDED.getClaimStatus()) {
                assignedUser = inspectionDetailsDto.getApproveAssignRteUser();
                if (null == assignedUser || assignedUser.isEmpty()) {
                    assignedUser = inspectionDetailsDto.getAssignRteUser();
                }
                boolean rteApproved = inspectionDetailsDao.isRteApproved(connection, refNo);
                success = claimHandlerDao.updateClaimApproveAssignUserByrefNo(connection, refNo, assignUser, rteApproved);
                if (!rteApproved) {
                    success = assessorAllocationDao.updateRecordStatusByJobId(connection, ClaimStatus.INSPECTION_FORWARDED.getClaimStatus(), refNo);
                }
            } else {
                assignedUser = inspectionDetailsDto.getAssignRteUser();
                success = claimHandlerDao.updateClaimAssignUserByrefNo(connection, refNo, assignUser);
            }
            sbMessage.append("You have Re-Assigned new inspection report.");
            saveNotification(connection, ClaimNo, user.getUserId(), assignUser, sbMessage.toString(), URL);
            initialLog(inspectionDetailsDto, connection, user, "Reassign RTE", assignedUser.concat(" -> ").concat(assignUser));

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return success;
    }

    @Override
    public String updateSelfAssignRTEUser(Integer refNo, String assignUser, UserDto user, Integer claimNo) throws Exception {
        String URL = AppConstant.MOTORENG_VIEW.concat("?P_N_REF_NO=").concat(Integer.toString(refNo));
        StringBuilder sbMessage = new StringBuilder();
        Connection connection = getJDBCConnection();
        boolean success = false;
        String assignedUser = null;
        String resultMessage = "FAIL";  // default fail

        try {
            beginTransaction(connection);
            InspectionDetailsDto inspectionDetailsDto = inspectionDetailsDao.searchMaster(connection, refNo);

            // Check if user already assigned claim_inspection_info_main
            boolean alreadyAssigned = claimHandlerDao.isUserAlreadyAssigned(connection, refNo, assignUser);

            if (alreadyAssigned) {
//                sbMessage.append("Inspection report is already assigned.");
//                saveNotification(connection, claimNo, user.getUserId(), assignUser, sbMessage.toString(), URL);
                resultMessage = "ALREADY_ASSIGNED";

            } else {
                if (inspectionDetailsDto.getRecordStatus() == ClaimStatus.INSPECTION_FORWARDED.getClaimStatus()) {
                    assignedUser = inspectionDetailsDto.getApproveAssignRteUser();
                    if (assignedUser == null || assignedUser.isEmpty()) {
                        assignedUser = inspectionDetailsDto.getAssignRteUser();
                    }
                    boolean rteApproved = inspectionDetailsDao.isRteApproved(connection, refNo);
                    success = claimHandlerDao.updateClaimApproveAssignUserByrefNo(connection, refNo, assignUser, rteApproved);
                    if (!rteApproved) {
                        success = assessorAllocationDao.updateRecordStatusByJobId(connection, ClaimStatus.INSPECTION_FORWARDED.getClaimStatus(), refNo);
                    }
                } else {
                    assignedUser = inspectionDetailsDto.getAssignRteUser();
                    success = claimHandlerDao.updateClaimAssignUserByrefNo(connection, refNo, assignUser);
                }

                if (success) {
                    sbMessage.append("You have successfully assigned the inspection report to yourself.");
                    saveNotification(connection, claimNo, user.getUserId(), assignUser, sbMessage.toString(), URL);
                    initialLog(inspectionDetailsDto, connection, user, "Reassign RTE", assignedUser.concat(" -> ").concat(assignUser));
                    resultMessage = "SUCCESS";
                }
            }

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw new MisynJDBCException(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }

        return resultMessage;
    }

    @Override
    public String updateSelfAssignCallCenterUser(String assignUser, UserDto user, Integer claimNo, Integer inspectionId) throws Exception {

        Connection connection = getJDBCConnection();
        String resultMessage = "FAIL";  // default fail

        try {
            beginTransaction(connection);

            boolean alreadyAssigned = commonBasketDao.isCallCenterUserExist(connection, claimNo, inspectionId);


            if (alreadyAssigned) {
                boolean isAssignedUser = commonBasketDao.isAssignedUser(connection, claimNo, inspectionId, user.getUserId());
                if (isAssignedUser) {
                    resultMessage = "ASSIGNED_USER";
                } else {
                    resultMessage = "ALREADY_ASSIGNED";
                }

            } else {
                String assignedUser = user.getUserId();

                boolean success = commonBasketDao.updateCallCenterUser(connection, assignedUser, assignedUser, inspectionId, claimNo);


                if (success) {
//                    sbMessage.append("You have successfully assigned the onsite review inspection report to yourself.");
//                    saveNotification(connection, claimNo, user.getUserId(), assignUser, sbMessage.toString(), URL);
//                    initialLog(inspectionDetailsDto, connection, user, "Assign Call Center", assignedUser.concat(" -> ").concat(assignUser));
                    resultMessage = "SUCCESS";
                }
            }

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw new MisynJDBCException(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }

        return resultMessage;
    }

    @Override
    public boolean isOnSiteOrOffSIteInspectionApproved(Integer claimNo) throws Exception {
        Connection connection = null;
        boolean isApprovedOnOrOffSiteInspection = false;
        try {
            connection = getJDBCConnection();
            isApprovedOnOrOffSiteInspection = inspectionDetailsDao.isOnSiteOrOffSIteInspectionApproved(connection, claimNo);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return isApprovedOnOrOffSiteInspection;
    }

    @Override
    public ContactDetailDto getContactDetailForRte(String assignRteUser) throws Exception {
        Connection connection = null;
        ContactDetailDto contactDetailDto = new ContactDetailDto();

        try {
            connection = getJDBCConnection();
            contactDetailDto = motorEngineerDetailsDao.getContactDetails(connection, assignRteUser);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return contactDetailDto;
    }

    @Override
    public boolean isAcrCanBeApprove(Integer inspectionId, BigDecimal totalAcr, BigDecimal sumInsured, BigDecimal pav, BigDecimal totalApprovedAcr, Integer claimNo) throws ErrorMsgException {

        boolean acrApprove = false;
        BigDecimal previousPav = BigDecimal.ZERO;
        try {
            switch (inspectionId) {
                case AppConstant.ON_SITE_INSPECTION:
                case AppConstant.SUERVAY_INSPECTION:
                case AppConstant.ADDITIONAL_INSPECTION:
                case AppConstant.VERTUAL_GARAGE_INSPECTION:
                case AppConstant.LOCATION_INSPECTION:
                case AppConstant.OFF_SITE_INSPECTION:
                case AppConstant.GARAGE_INSPECTION:
                case AppConstant.DESKTOP_INSPECTION:
                    acrApprove = isPavAndSumInsuredGreaterThanAcr(sumInsured, pav, totalAcr);
                    break;
                case AppConstant.DR_INSPECTION:
                case AppConstant.SUP_INSPECTION:
                    previousPav = getPreviousPav(claimNo);
                    totalAcr = totalAcr.add(totalApprovedAcr);
                    acrApprove = isPavAndSumInsuredGreaterThanAcr(sumInsured, previousPav, totalAcr);
                    break;
                case AppConstant.ARI_INSPECTION:
                case AppConstant.SALVAGE_INSPECTION:
                    acrApprove = true;
                    break;
                default:
                    break;
            }
            if (acrApprove) {
                return true;
            } else {
                throw new ErrorMsgException(AppConstant.ERROR_MESSAGE, "ACR Value Cannot be Greater than Sum Insured or Pav");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new ErrorMsgException(AppConstant.ERROR_MESSAGE, "ACR Value Cannot be Greater than Sum Insured or Pav");
        }
    }

    private BigDecimal getPreviousPav(Integer claimNo) {
        BigDecimal pav = BigDecimal.ZERO;
        Connection connection = null;

        try {
            connection = getJDBCConnection();
            pav = motorEngineerDetailsDao.getPreviousPavFromMe(connection, claimNo);

            if (null == pav || BigDecimal.ZERO == pav) {
                pav = motorEngineerDetailsDao.getPreviousPav(connection, claimNo);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return pav;
    }

    @Override
    public BigDecimal getTotalAprvAcrAmount(Integer refNo) {
        Connection connection = null;
        BigDecimal totalAprvAcr = BigDecimal.ZERO;
        try {
            connection = getJDBCConnection();
            Integer claimNo = motorEngineerDetailsDao.getClaimNoByRefNo(connection, refNo);
            if (null != claimNo) {
                boolean isRteApproved = motorEngineerDetailsDao.isRteApprovedData(connection, claimNo);
                if (isRteApproved) {
                    totalAprvAcr = claimHandlerDao.getTotalAcrAmount(connection, claimNo);
                }
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            return totalAprvAcr;
        } finally {
            releaseJDBCConnection(connection);
        }
        return totalAprvAcr;
    }

    @Override
    public MotorEngineerDetailsDto setAdvanceAmountDetails(MotorEngineerDetailsDto motorEngineerDetailsDto, Integer claimNo) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            motorEngineerDetailsDto = setAdvanceAmountDetails(connection, motorEngineerDetailsDto, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return motorEngineerDetailsDto;
    }

    @Override
    public List<String> getIdsToArray(String selectedIds) {
        return getSelectedList("RRB", selectedIds);
    }

    @Override
    public HashMap<String, String> setClaimNoandJobNoToMap(String selectIds) {
        List<String> claimNoAndJobNoStr = getSelectedList("RRB", selectIds);
        HashMap<String, String> map = new HashMap<>();

        if (null != claimNoAndJobNoStr) {
            for (String valueStr : claimNoAndJobNoStr) {
                String key = after(valueStr, "-").trim();
                String value = before(valueStr, "-").trim();
                map.put(key, value);
            }
        }
        return map;
    }

    @Override
    public boolean checkPendingInspection(Integer claimNo) {
        Connection connection = null;
        boolean pendingInspection = false;
        try {
            connection = getJDBCConnection();
//            pendingInspection = motorEngineerDetailsDao.isInspectionPending(connection, claimNo);
            pendingInspection = rtePendingClaimDetailDao.checkIfRteJobsPending(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            releaseJDBCConnection(connection);
        }
        return pendingInspection;
    }

    @Override
    public boolean isForwardInspection(BigDecimal acr, Integer refNo, Integer inspectionTypeId, UserDto user) {
        Connection connection = null;
        BigDecimal totalApprovedAcr;
        try {
            connection = getJDBCConnection();
            boolean isAlreadySentChangeRequest = changeRequestDetailDao.isChangeRequested(connection, refNo);
            InspectionDetailsDto inspectionDetailsDto = inspectionDetailsDao.searchMaster(connection, refNo);
            boolean isAvailableApprovedGarageInspection = motorEngineerDetailsDao.isAvailableApprovedInspectionByInspectionType(connection, inspectionDetailsDto.getClaimNo(), AppConstant.GARAGE_INSPECTION);
            boolean isAvailableApprovedDesktopInspection = motorEngineerDetailsDao.isAvailableApprovedInspectionByInspectionType(connection, inspectionDetailsDto.getClaimNo(), AppConstant.DESKTOP_INSPECTION);
            boolean isAvailableApprovedDrInspection = motorEngineerDetailsDao.isAvailableApprovedInspectionByInspectionType(connection, inspectionDetailsDto.getClaimNo(), AppConstant.DR_INSPECTION);
            boolean isAvailableApprovedSupplimantaryInspection = motorEngineerDetailsDao.isAvailableApprovedInspectionByInspectionType(connection, inspectionDetailsDto.getClaimNo(), AppConstant.SUP_INSPECTION);
            ClaimHandlerDto searchedClaimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, inspectionDetailsDto.getClaimNo());
            BigDecimal changeAcr;
            totalApprovedAcr = searchedClaimHandlerDto.getAprvTotAcrAmount();
            switch (inspectionTypeId) {
                case AppConstant.ON_SITE_INSPECTION://On site Inspection = 1
                case AppConstant.OFF_SITE_INSPECTION://Off site Inspection = 2
                    return false;
                case AppConstant.CALL_ESTIMATE://Call Estimate = 11
                    OnSiteInspectionDetailsDto prevOnSiteInspectionDetailsDto = onSiteInspectionDetailsMeDao.searchMaster(connection, refNo);
                    if (!isAvailableApprovedGarageInspection && !isAvailableApprovedDesktopInspection && !isAvailableApprovedDrInspection && !isAvailableApprovedSupplimantaryInspection) {
                        if (inspectionDetailsDto.getRecordStatus() == ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus() || isAlreadySentChangeRequest) {
                            if (acr.compareTo(prevOnSiteInspectionDetailsDto.getOldAcr()) > 0) {
                                changeAcr = acr.subtract(prevOnSiteInspectionDetailsDto.getOldAcr());
                            } else {
                                changeAcr = acr.subtract(prevOnSiteInspectionDetailsDto.getOldAcr());
                            }
                            totalApprovedAcr = searchedClaimHandlerDto.getAprvTotAcrAmount().add(changeAcr);
                        } else {
                            totalApprovedAcr = acr;
                        }
                    }
                    break;

                case AppConstant.GARAGE_INSPECTION://Garage Inspection - ACR Reset = 4
                    if (inspectionDetailsDto.getRecordStatus() == ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus() || isAlreadySentChangeRequest) {
                        GarageInspectionDetailsDto prevGarageInspectionDetailsDto = garageInspectionDetailsMeDao.searchMaster(connection, refNo);
                        if (acr.compareTo(prevGarageInspectionDetailsDto.getOldAcr()) > 0) {
                            changeAcr = acr.subtract(prevGarageInspectionDetailsDto.getOldAcr());
                        } else {
                            changeAcr = acr.subtract(prevGarageInspectionDetailsDto.getOldAcr());
                        }
                        totalApprovedAcr = searchedClaimHandlerDto.getAprvTotAcrAmount().add(changeAcr);
                    } else {
                        totalApprovedAcr = acr;
                    }
                    break;

                case AppConstant.DESKTOP_INSPECTION://Desktop  Inspection - ACR Reset = 8
                    if ((inspectionDetailsDto.getRecordStatus() == ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus() || isAlreadySentChangeRequest)) {
                        DesktopInspectionDetailsDto prevDesktopInspectionDetailsDto = desktopInspectionDetailsDao.searchMaster(connection, refNo);
                        if (acr.compareTo(prevDesktopInspectionDetailsDto.getOldAcr()) > 0) {
                            changeAcr = acr.subtract(prevDesktopInspectionDetailsDto.getOldAcr());
                        } else {
                            changeAcr = acr.subtract(prevDesktopInspectionDetailsDto.getOldAcr());
                        }
                        totalApprovedAcr = searchedClaimHandlerDto.getAprvTotAcrAmount().add(changeAcr);
                    } else {
                        totalApprovedAcr = acr;
                    }
                    break;

                case AppConstant.DR_INSPECTION://DR Insepection = 5
                case AppConstant.SUP_INSPECTION://Supplimantary Inspection = 6
                    DrSupplementaryInspectionDetailsDto prevDrSupplementaryInspectionDetailsDto = drSupplementaryInspectionDetailsMeDao.searchMaster(connection, refNo);
                    if (null != prevDrSupplementaryInspectionDetailsDto && (inspectionDetailsDto.getRecordStatus() == ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus() || isAlreadySentChangeRequest)) {
                        if (acr.compareTo(prevDrSupplementaryInspectionDetailsDto.getOldAcr()) > 0) {
                            changeAcr = acr.subtract(prevDrSupplementaryInspectionDetailsDto.getOldAcr());
                        } else {
                            changeAcr = getChangeAcrAmountIfAlreadyApprovedPayment(connection, acr, prevDrSupplementaryInspectionDetailsDto.getOldAcr(), searchedClaimHandlerDto.getReserveAmount(), searchedClaimHandlerDto.getClaimNo());
                        }
                        totalApprovedAcr = searchedClaimHandlerDto.getAprvTotAcrAmount().add(changeAcr);
                    } else {
                        totalApprovedAcr = searchedClaimHandlerDto.getAprvTotAcrAmount().add(acr);
                    }
                    break;
            }

            UserAuthorityLimitDto userAuthorityLimitDto = userAuthorityLimitDao.searchByLevelCodeAndDepartmentId(connection, user.getRteReserveLimitLevel(), AppConstant.MOTOR_ENGINEER_DEPARTMENT_ID);
            if (null != totalApprovedAcr && totalApprovedAcr.compareTo(userAuthorityLimitDto.getToLimit()) > 0) {
                return true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return false;
    }

    @Override
    public MotorEngineerDetailsDto insert(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user, boolean isForward, BigDecimal premium, boolean isCancelled) throws Exception {
        Connection connection = null;
        boolean isAlreadySentChangeRequest;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);

            isAlreadySentChangeRequest = changeRequestDetailDao.isChangeRequested(connection, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());

            //Set Audit Details
            motorEngineerDetailsDto.setInputDatetime(Utility.sysDate(AppConstant.DATE_TIME_FORMAT));
            motorEngineerDetailsDto.setInputUserId(user.getUserId());

            //Set Nested Objects Values To Most Outer Object
            motorEngineerDetailsDto.setRefNo(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
//            motorEngineerDetailsDto.setJobId(motorEngineerDetailsDto.getAssessorAllocationDto().getJobId());
            motorEngineerDetailsDto.setClaimNo(motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
            motorEngineerDetailsDto.getInspectionDto().setInspectionId(motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId());
            // inspectionDetailsDto.setInspectionId(inspectionDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId());

            if (!AppConstant.SAVE_REPORT.equals(motorEngineerDetailsDto.getActionType())) {
                switch (motorEngineerDetailsDto.getActionType()) {
                    case AppConstant.AUTH_INSPECTION_DETAILS:
                        inspectionDetailsDao.updateInspectionDetailAuthByRefNo(connection, motorEngineerDetailsDto.getInspectionDetailsAuthStatus(), motorEngineerDetailsDto.getInspectionDetailsAuthUserId(), motorEngineerDetailsDto.getInspectionDetailsAuthDatetime(), motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());
                        // 8 --> Desktop Inspection
                        if (ClaimStatus.APPROVED.getClaimStatus() != motorEngineerDetailsDto.getInspectionDetailsDto().getRecordStatus() && "A".equals(motorEngineerDetailsDto.getInspectionDetailsDto().getInspectionDetailsAuthStatus())) {
                            //Set and Updated Assessor Inspection Detail Record Status as 9 --> APPROVED
                            motorEngineerDetailsDto.setRecordStatus(ClaimStatus.APPROVED.getClaimStatus());

                            //Set and Updated Assessor allocation Record Status as 9 --> APPROVED
                            assessorAllocationDao.updateRecordStatusByRefNo(connection, ClaimStatus.APPROVED.getClaimStatus(), motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
                            //Update Job Finished Date For Desktop Inspections
                            if (AppConstant.DESKTOP_INSPECTION == motorEngineerDetailsDto.getInspectionDto().getInspectionId()) {
                                assessorAllocationDao.updateJobFinishedDate(connection, motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());
                            }
                            boolean isChangeRequest = true;
                            if (motorEngineerDetailsDto.getInspectionDto().getInspectionId() != AppConstant.ARI_INSPECTION && motorEngineerDetailsDto.getInspectionDto().getInspectionId() != AppConstant.SALVAGE_INSPECTION && rtePendingClaimDetailDao.checkIfRteJobsPending(connection, motorEngineerDetailsDto.getClaimNo())) {
                                sendNotificationOnAuthorizedInspections(connection, motorEngineerDetailsDto.getClaimNo(), user, true, motorEngineerDetailsDto.getInspectionDetailsDto().getInspectionId());
                                isChangeRequest = false;
                            }
                            //Set and Updated Assessor allocation Record Status as 9 --> APPROVED
                            inspectionDetailsDao.updateRecordStatusByRefNo(connection, ClaimStatus.APPROVED.getClaimStatus(), motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());

                            updateClaimHandler(connection, motorEngineerDetailsDto, user, isChangeRequest, isAlreadySentChangeRequest, premium, isCancelled);

                        } else if (isForward) {
                            motorEngineerDetailsDto.setRecordStatus(ClaimStatus.INSPECTION_FORWARDED.getClaimStatus());

                            //Set and Updated Assessor allocation Record Status as 80 --> INSPECTION FORWARDED
                            assessorAllocationDao.updateRecordStatusByRefNo(connection, ClaimStatus.INSPECTION_FORWARDED.getClaimStatus(), motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());

                            //Set and Updated Inspection_info_main Record Status as 80 --> INSPECTION FORWARDED
                            inspectionDetailsDao.updateRecordStatusByRefNo(connection, ClaimStatus.INSPECTION_FORWARDED.getClaimStatus(), motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());
                        }
                        //Save Inspection Details
                        saveInspectionDetails(motorEngineerDetailsDto, connection);//Only Save Inspection Details
                        break;
                    case AppConstant.AUTH_ASSESSOR_FEE:
                        inspectionDetailsDao.updateAssessorFeeAuthByRefNo(connection, motorEngineerDetailsDto.getAssessorFeeAuthStatus(), motorEngineerDetailsDto.getAssessorFeeAuthUserId(), motorEngineerDetailsDto.getAssessorFeeAuthDatetime(), motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());
                        offlineAssessorFee(connection, motorEngineerDetailsDto);
                        manageAssessorPayement(connection, motorEngineerDetailsDto, user);

                        //Save Inspection Details
                        saveAssessorFeeInspectionDetails(connection, motorEngineerDetailsDto); //Only Save Assessor Fee
                        break;
                }

            } else {
                saveInspectionReportDetails(connection, motorEngineerDetailsDto); //Only Save Inspection Report Details
            }

            // Update Online Inspection Detail
//            assessorAllocationDao.updateOnlineInspectionStatus(connection, motorEngineerDetailsDto.getTypeOnlineInspection(), motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());

            //8 --> Desktop Inspection
            if (AppConstant.SAVE_REPORT.equals(motorEngineerDetailsDto.getActionType()) && 8 == motorEngineerDetailsDto.getInspectionDto().getInspectionId()) {
                //Set and Updated Assessor Inspection Detail Record Status as 29 --> ASSIGNED
                motorEngineerDetailsDto.setRecordStatus(29);
                motorEngineerDetailsDto.getInspectionDetailsDto().setRecordStatus(29);

                //Set and Updated Assessor allocation Record Status as 29 --> ASSIGNED
                inspectionDetailsDao.updateRecordStatusByRefNo(connection, 29, motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());

            } else if (!AppConstant.SAVE_REPORT.equals(motorEngineerDetailsDto.getActionType()) && !AppConstant.AUTH_ASSESSOR_FEE.equals(motorEngineerDetailsDto.getActionType())) {

//                saveTyreCondition(motorEngineerDetailsDto.getTireCondtionDtoList(), connection);
//
//                //save special remarks separately on insert and update record
//                saveSpecialRemark(motorEngineerDetailsDto, user, connection);

                //save third Party Assessor Dtos
                saveThirdPartyAssessorDetails(motorEngineerDetailsDto, user, connection);

                //Save Assessor Estimate For Repair as selected by Inspection Type
                BigDecimal forwardAcr = BigDecimal.ZERO;
                switch (motorEngineerDetailsDto.getInspectionDto().getInspectionId()) {
                    case 4: //Garage Inspection
                        garageInspectionDetails(connection, motorEngineerDetailsDto, user, isForward, isAlreadySentChangeRequest);
                        forwardAcr = null == motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr() ? BigDecimal.ZERO : motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr();
                        break;
                    case 5: //DR Inspection
                    case 6: //Supplementary Inspection
                        drSupplementaryInspectionDetails(connection, motorEngineerDetailsDto, user, isForward, isAlreadySentChangeRequest);
                        forwardAcr = null == motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAcr() ? BigDecimal.ZERO : motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAcr();
                        break;
                    case 7: //After Repair inspection
                    case 9: //Salvage Inspection
                        ariSalvageInspectionDetails(connection, motorEngineerDetailsDto, user);
                        break;
                    case 8: //Desktop Assessment
                        desktopInspectionDetails(connection, motorEngineerDetailsDto, user, isForward, isAlreadySentChangeRequest);
                        ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, motorEngineerDetailsDto.getClaimNo());
                        if (!isForward && motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getDesktopOffer() == ConditionType.valueOf("Yes") && claimHandlerDto.getIsFileStore().equals(AppConstant.YES) && claimHandlerDto.getFileUserStoreUserId() != null && ((claimHandlerDto.getFileUserStoreUserId().equals(claimHandlerDto.getInitLiabilityAssignUserId()) && claimHandlerDto.getInitLiabilityAprvStatus().equals(AppConstant.APPROVE)) || (claimHandlerDto.getFileUserStoreUserId().equals(claimHandlerDto.getDecisionMakingAssignUserId()) && claimHandlerDto.getLiabilityAprvStatus().equals(AppConstant.APPROVE)))) {
                            sendOfferSms(connection, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getPayableAmount(), user);
                        }
                        if (motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getDesktopOffer() == ConditionType.valueOf("No")) {
                            this.sendDesktopAssessmentSmsToCustomer(motorEngineerDetailsDto, connection, user);
                            this.sendDesktopAssessmentSmsToAgent(motorEngineerDetailsDto, connection, user);
                        }
                        forwardAcr = null == motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAcr() ? BigDecimal.ZERO : motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAcr();
                        boolean isCallCenterNotifyOnsiteReview = "Y".equals(motorEngineerDetailsDto.getIsOnsiteReviewApply());
                        if (isCallCenterNotifyOnsiteReview) {

                            AssessorAllocationDto allocationDto = motorEngineerDetailsDto.getAssessorAllocationDto();

                            int inspectionId = motorEngineerDetailsDto.getInspectionId();
                            int inspectionType = motorEngineerDetailsDto.getInspectionDto().getInspectionId();
                            int refNo = allocationDto.getRefNo();

                            String URL = AppConstant.CLAIM_VIEW + "?P_N_CLIM_NO=" + motorEngineerDetailsDto.getClaimNo() + "&P_TAB_INDEX=2" + "&TYPE=2" + "&onsiteReview=true" + "&requestedInspectionId=" + inspectionId;

                            List<String> userIds = getActiveCallCenterUserIds(connection);
                            for (String userId : userIds) {
                                saveNotification(connection, motorEngineerDetailsDto.getClaimNo(), user.getUserId(), userId, "  Notify For Onsite Review. ", URL, refNo);
                            }
                            CallCenterCommonBasketDto commonBasketDto = new CallCenterCommonBasketDto();
                            commonBasketDto.setRteUserId(user.getUserId());
                            commonBasketDto.setInspectionId(inspectionId);
                            commonBasketDto.setInspectionType(inspectionType);
                            commonBasketDto.setClaimNo(motorEngineerDetailsDto.getClaimNo());
                            commonBasketDto.setSubmittedUser(user.getUserId());
                            commonBasketDto.setRejectedReason(AppConstant.STRING_EMPTY);
                            commonBasketDto.setLastUpdatedUser(user.getUserId());
                            commonBasketDto.setRejectCount(AppConstant.ZERO_INT);

                            commonBasketDao.insertCommonBasket(connection, commonBasketDto);

                        }
                        break;
                    default://On site Inspection
                        if (motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getOfferType() == 3) {
                            this.sendCallEstimateAssignedSmsToAgent(motorEngineerDetailsDto, connection, user);
                            this.sendCallEstimateAssignedSmsToCustomer(motorEngineerDetailsDto, connection, user);
                        }
                        onsiteInspectionDetails(connection, motorEngineerDetailsDto, user, isForward, isAlreadySentChangeRequest);
                        forwardAcr = null == motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr() ? BigDecimal.ZERO : motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr();
                        break;
                }

                if (isForward && AppConstant.AUTH_INSPECTION_DETAILS.equalsIgnoreCase(motorEngineerDetailsDto.getActionType())) {
                    Integer authLevel = null;
                    authLevel = inspectionDetailsDao.getForwardedAuthLimit(connection, motorEngineerDetailsDto.getRefNo(), false);
                    if (null == authLevel || authLevel == AppConstant.ZERO_INT) {
                        authLevel = inspectionDetailsDao.getForwardedAuthLimit(connection, motorEngineerDetailsDto.getRefNo(), true);
                    }
                    String URL = AppConstant.MOTORENG_VIEW.concat("?P_N_REF_NO=").concat(Integer.toString(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo()));
                    String assignUserId = motorEngineerDetailsDto.getInspectionDetailsDto().getAssignRteUser();
                    AuthAssignRteDto rteLevelDetails = authAssignRteDao.getRteLevelDetails(connection, assignUserId);
                    String nextLevelRte = getNextLevelRte(connection, rteLevelDetails, authLevel, forwardAcr);

                    if (!Objects.equals(nextLevelRte, AppConstant.STRING_EMPTY)) {
                        inspectionDetailsDao.updateForwardDetails(connection, nextLevelRte, Utility.sysDateTime(), motorEngineerDetailsDto.getRefNo());
                        saveNotification(connection, motorEngineerDetailsDto.getClaimNo(), user.getUserId(), nextLevelRte, "        You have an Inspection for ACR Approval. ", URL, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
                        initialLog(motorEngineerDetailsDto.getInspectionDetailsDto(), connection, user, "Inspection Forwarded", "Inspection forwarded by " + user.getUserId() + " to [ " + nextLevelRte + " ( acr - " + forwardAcr + ")]");
                    } else {
                        throw new UserNotFoundException(AppConstant.ERROR_MESSAGE, "User not found to assign claim");
                    }
                }
                //update all saved NEW third Party Assessor Dtos to EDIT Mode
                updateThirdPartyAssessorDetailsStatusToEditMode(motorEngineerDetailsDto);
            }
//            saveTyreCondition(motorEngineerDetailsDto.getTireCondtionDtoList(), connection);
            updateTyreCondition(motorEngineerDetailsDto.getTireCondtionDtoList(), connection, motorEngineerDetailsDto.getInputUserId(), motorEngineerDetailsDto);

            //save special remarks separately on insert and update record
            saveSpecialRemark(motorEngineerDetailsDto, user, connection);

            commitTransaction(connection);
        } catch (Exception ex) {
            rollbackTransaction(connection);
            LOGGER.error(ex.getMessage(), ex);
            throw ex;
        } finally {
            releaseJDBCConnection(connection);
        }
        return motorEngineerDetailsDto;
    }

    private List<String> getActiveCallCenterUserIds(Connection conn) throws SQLException {
        List<String> userIds = new ArrayList<>();
        String sql = "SELECT v_usrid FROM usr_mst WHERE n_accessusrtype=10 AND v_usrstatus = 'A'";
        try (PreparedStatement ps = conn.prepareStatement(sql); ResultSet rs = ps.executeQuery()) {
            while (rs.next()) {
                userIds.add(rs.getString("v_usrid"));
            }
        }
        return userIds;
    }

    private void sendCallEstimateAssignedSmsToAgent(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection, UserDto user) throws Exception {
        List<String> smsParameterList = new ArrayList<>();
        sendSmsMessage(connection, 45, smsParameterList, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getPolicySellingAgentDetailsDto().getContactNo(), motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getPolicyChannelType(), user, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), AppConstant.SEND_TO_AGENT);
    }

    private void sendCallEstimateAssignedSmsToCustomer(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection, UserDto user) throws Exception {
        List<String> smsParameterList = new ArrayList<>();
        sendSmsMessage(connection, 44, smsParameterList, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getCustMobileNo(), motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getPolicyChannelType(), user, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), AppConstant.SEND_TO_CUSTOMER);
    }

    private void sendDesktopAssessmentSmsToCustomer(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection, UserDto user) throws Exception {
        List<String> smsParameterList = new ArrayList<>();
        sendSmsMessage(connection, 47, smsParameterList, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getCustMobileNo(), motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getPolicyChannelType(), user, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), AppConstant.SEND_TO_CUSTOMER);
    }

    private void sendDesktopAssessmentSmsToAgent(MotorEngineerDetailsDto motorEngineerDetailsDto, Connection connection, UserDto user) throws Exception {
        List<String> smsParameterList = new ArrayList<>();
        sendSmsMessage(connection, 48, smsParameterList, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getPolicySellingAgentDetailsDto().getContactNo(), motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getPolicyDto().getPolicyChannelType(), user, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo(), AppConstant.SEND_TO_AGENT);
    }

    @Override
    public MotorEngineerDetailsDto update(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user, boolean isForward, BigDecimal premium, boolean isCancelled) throws Exception {
        Connection connection = null;
        boolean isAlreadySentChangeRequest;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);

            isAlreadySentChangeRequest = changeRequestDetailDao.isChangeRequested(connection, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());

            //Set Audit Details
            motorEngineerDetailsDto.setInputDatetime(Utility.sysDateTime());
            motorEngineerDetailsDto.setInputUserId(user.getUserId());

            //Set Nested Objects Values To Most Outer Object
            motorEngineerDetailsDto.setRefNo(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
//            motorEngineerDetailsDto.setJobId(motorEngineerDetailsDto.getAssessorAllocationDto().getJobId());
            motorEngineerDetailsDto.setClaimNo(motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
            motorEngineerDetailsDto.getInspectionDto().setInspectionId(motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId());

            if (!AppConstant.SAVE_REPORT.equals(motorEngineerDetailsDto.getActionType())) {
                switch (motorEngineerDetailsDto.getActionType()) {
                    case AppConstant.AUTH_INSPECTION_DETAILS:
                        inspectionDetailsDao.updateInspectionDetailAuthByRefNo(connection, motorEngineerDetailsDto.getInspectionDetailsAuthStatus(), motorEngineerDetailsDto.getInspectionDetailsAuthUserId(), motorEngineerDetailsDto.getInspectionDetailsAuthDatetime(), motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());
                        if (9 != motorEngineerDetailsDto.getInspectionDetailsDto().getRecordStatus() && "A".equals(motorEngineerDetailsDto.getInspectionDetailsDto().getInspectionDetailsAuthStatus())) {
                            //Set and Updated Assessor Inspection Detail Record Status as 9 --> APPROVED
                            motorEngineerDetailsDto.setRecordStatus(9);
                            //Set and Updated Assessor allocation Record Status as 9 --> APPROVED
                            assessorAllocationDao.updateRecordStatusByRefNo(connection, 9, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
                            //Set and Updated inspection_info_main Record Status as 9 --> APPROVED
                            inspectionDetailsDao.updateRecordStatusByRefNo(connection, 9, motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());
                            //Set job finished Date Time for Desktop Inspections
                            if (AppConstant.DESKTOP_INSPECTION == motorEngineerDetailsDto.getInspectionDto().getInspectionId()) {
                                assessorAllocationDao.updateJobFinishedDate(connection, motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());
                            }
                            boolean isChangeRequest = true;
                            if (motorEngineerDetailsDto.getInspectionDto().getInspectionId() != AppConstant.ARI_INSPECTION && motorEngineerDetailsDto.getInspectionDto().getInspectionId() != AppConstant.SALVAGE_INSPECTION && rtePendingClaimDetailDao.checkIfRteJobsPending(connection, motorEngineerDetailsDto.getClaimNo())) {
                                sendNotificationOnAuthorizedInspections(connection, motorEngineerDetailsDto.getClaimNo(), user, true, motorEngineerDetailsDto.getInspectionDetailsDto().getInspectionId());
                                isChangeRequest = false;
                            }
                            updateClaimHandler(connection, motorEngineerDetailsDto, user, isChangeRequest, isAlreadySentChangeRequest, premium, isCancelled);
                        } else if (isForward) {
                            motorEngineerDetailsDto.setRecordStatus(ClaimStatus.INSPECTION_FORWARDED.getClaimStatus());

                            //Set and Updated Assessor allocation Record Status as 9 --> INSPECTION FORWARDED
                            assessorAllocationDao.updateRecordStatusByRefNo(connection, ClaimStatus.INSPECTION_FORWARDED.getClaimStatus(), motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());

                            //Set and Updated Inspection_info_main Record Status as 9 --> INSPECTION FORWARDED
                            inspectionDetailsDao.updateRecordStatusByRefNo(connection, ClaimStatus.INSPECTION_FORWARDED.getClaimStatus(), motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());
                        }
                        //Update Inspection Details
                        updateInspectionDetails(motorEngineerDetailsDto, connection); //Only Update Inspection Details
                        break;
                    case AppConstant.AUTH_ASSESSOR_FEE:
                        inspectionDetailsDao.updateAssessorFeeAuthByRefNo(connection, motorEngineerDetailsDto.getAssessorFeeAuthStatus(), motorEngineerDetailsDto.getAssessorFeeAuthUserId(), motorEngineerDetailsDto.getAssessorFeeAuthDatetime(), motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());
                        offlineAssessorFee(connection, motorEngineerDetailsDto);
                        manageAssessorPayement(connection, motorEngineerDetailsDto, user);

                        //Update Inspection Details
                        updateAssessorFeeInspectionDetails(motorEngineerDetailsDto, connection); //Only Update Assessor Fee
                        break;
                }

            } else {
                //Update Inspection Details
                updateInspectionReportDetails(motorEngineerDetailsDto, connection); //Only Update Inspection Report Details
            }

            // Update Online Inspection Detail
//            boolean onlineInspectionStatus =assessorAllocationDao.updateOnlineInspectionStatus(connection, motorEngineerDetailsDto.getTypeOnlineInspection(), motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
            boolean isCallCenterNotifyOnsiteReview = "Y".equals(motorEngineerDetailsDto.getIsOnsiteReviewApply());
            //8 --> Desktop Inspection
            if (AppConstant.SAVE_REPORT.equals(motorEngineerDetailsDto.getActionType()) && 8 == motorEngineerDetailsDto.getInspectionDto().getInspectionId()) {
                //Set and Updated Assessor Inspection Detail Record Status as 29 --> ASSIGNED
                motorEngineerDetailsDto.setRecordStatus(29);
                motorEngineerDetailsDto.getInspectionDetailsDto().setRecordStatus(29);

                //Set and Updated Assessor allocation Record Status as 29 --> ASSIGNED
                inspectionDetailsDao.updateRecordStatusByRefNo(connection, 29, motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());
            } else if (!AppConstant.SAVE_REPORT.equals(motorEngineerDetailsDto.getActionType()) && !AppConstant.AUTH_ASSESSOR_FEE.equals(motorEngineerDetailsDto.getActionType())) {

//                updateTyreCondition(motorEngineerDetailsDto.getTireCondtionDtoList(), connection, motorEngineerDetailsDto.getInputUserId(), motorEngineerDetailsDto);
//
//                //save special remarks separately on insert and update record
//                saveSpecialRemark(motorEngineerDetailsDto, user, connection);

                //save third Party Assessor Dtos
                saveThirdPartyAssessorDetails(motorEngineerDetailsDto, user, connection);
                BigDecimal forwardAcr = BigDecimal.ZERO;

                switch (motorEngineerDetailsDto.getInspectionDto().getInspectionId()) {
                    case 4: //Garage Inspection
                        garageInspectionDetails(connection, motorEngineerDetailsDto, user, isForward, isAlreadySentChangeRequest);
                        forwardAcr = null == motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr() ? BigDecimal.ZERO : motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr();
                        break;
                    case 5: //DR Inspection
                    case 6: //Supplementary Inspection
                        drSupplementaryInspectionDetails(connection, motorEngineerDetailsDto, user, isForward, isAlreadySentChangeRequest);
                        forwardAcr = null == motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAcr() ? BigDecimal.ZERO : motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAcr();
                        break;
                    case 7: //After Repair inspection
                    case 9:    //Salvage Inspection
                        ariSalvageInspectionDetails(connection, motorEngineerDetailsDto, user);
                        break;
                    case 8: //Desktop Assessment
                        desktopInspectionDetails(connection, motorEngineerDetailsDto, user, isForward, isAlreadySentChangeRequest);
                        forwardAcr = null == motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAcr() ? BigDecimal.ZERO : motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAcr();
                        break;
                    default://On site Inspection
                        onsiteInspectionDetails(connection, motorEngineerDetailsDto, user, isForward, isAlreadySentChangeRequest);
                        forwardAcr = null == motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr() ? BigDecimal.ZERO : motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr();
                        break;
                }

                if (isForward && AppConstant.AUTH_INSPECTION_DETAILS.equalsIgnoreCase(motorEngineerDetailsDto.getActionType())) {
                    Integer authLevel;
                    authLevel = inspectionDetailsDao.getForwardedAuthLimit(connection, motorEngineerDetailsDto.getRefNo(), false);
                    if (null == authLevel || authLevel == AppConstant.ZERO_INT) {
                        authLevel = inspectionDetailsDao.getForwardedAuthLimit(connection, motorEngineerDetailsDto.getRefNo(), true);
                    }
                    String URL = AppConstant.MOTORENG_VIEW.concat("?P_N_REF_NO=").concat(Integer.toString(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo()));
                    AuthAssignRteDto rteLevelDetails = null;
                    String assignUserId = motorEngineerDetailsDto.getInspectionDetailsDto().getAssignRteUser();
                    rteLevelDetails = authAssignRteDao.getRteLevelDetails(connection, assignUserId);
                    String nextLevelRte = getNextLevelRte(connection, rteLevelDetails, authLevel, forwardAcr);

                    if (!Objects.equals(nextLevelRte, AppConstant.STRING_EMPTY)) {
                        inspectionDetailsDao.updateForwardDetails(connection, nextLevelRte, Utility.sysDateTime(), motorEngineerDetailsDto.getRefNo());
                        saveNotification(connection, motorEngineerDetailsDto.getClaimNo(), user.getUserId(), nextLevelRte, "You have an Inspection for ACR Approval. ", URL, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
                        initialLog(motorEngineerDetailsDto.getInspectionDetailsDto(), connection, user, "Inspection Forwarded", "Inspection forwarded by " + user.getUserId() + " to [ " + nextLevelRte + " ( acr - " + forwardAcr + " )]");
                    } else {
                        throw new UserNotFoundException(AppConstant.ERROR_MESSAGE, "User not found to assign claim");
                    }
                }
                //update all saved NEW third Party Assessor Dtos to EDIT Mode
                updateThirdPartyAssessorDetailsStatusToEditMode(motorEngineerDetailsDto);
            }
            updateTyreCondition(motorEngineerDetailsDto.getTireCondtionDtoList(), connection, motorEngineerDetailsDto.getInputUserId(), motorEngineerDetailsDto);
            //save special remarks separately on insert and update record
            saveSpecialRemark(motorEngineerDetailsDto, user, connection);
            if (isCallCenterNotifyOnsiteReview) {

                AssessorAllocationDto allocationDto = motorEngineerDetailsDto.getAssessorAllocationDto();

                int inspectionId = motorEngineerDetailsDto.getInspectionId();
                int inspectionType = motorEngineerDetailsDto.getInspectionDto().getInspectionId();
                int refNo = allocationDto.getRefNo();

                String URL = AppConstant.CLAIM_VIEW + "?P_N_CLIM_NO=" + motorEngineerDetailsDto.getClaimNo() + "&P_TAB_INDEX=2" + "&TYPE=2" + "&onsiteReview=true" + "&requestedInspectionId=" + inspectionId;

                List<String> userIds = getActiveCallCenterUserIds(connection);
                for (String userId : userIds) {
                    saveNotification(connection, motorEngineerDetailsDto.getClaimNo(), user.getUserId(), userId, "  Notify For Onsite Review. ", URL, refNo);
                }
                CallCenterCommonBasketDto commonBasketDto = new CallCenterCommonBasketDto();
                commonBasketDto.setRteUserId(user.getUserId());
                commonBasketDto.setInspectionId(inspectionId);
                commonBasketDto.setInspectionType(inspectionType);
                commonBasketDto.setClaimNo(motorEngineerDetailsDto.getClaimNo());
                commonBasketDto.setSubmittedUser(user.getUserId());

                commonBasketDao.insertCommonBasket(connection, commonBasketDto);

            }

            commitTransaction(connection);
        } catch (Exception ex) {
            rollbackTransaction(connection);
            LOGGER.error(ex.getMessage(), ex);
            throw ex;
        } finally {
            releaseJDBCConnection(connection);
        }
        return motorEngineerDetailsDto;
    }

    @Override
    public boolean returnByApproveAssignRte(MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) throws Exception {
        Connection connection = null;
        boolean isSuccess = false;
        int status;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            String URL = AppConstant.MOTORENG_VIEW.concat("?P_N_REF_NO=").concat(Integer.toString(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo()));

            boolean isChangeRequested = changeRequestDetailDao.isChangeRequested(connection, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
            if (isChangeRequested) {
                status = ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus();
            } else {
                if (AppConstant.DESKTOP_INSPECTION == motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId()) {
                    status = ClaimStatus.JOB_ASSIGNED.getClaimStatus();
                } else {
                    status = ClaimStatus.SUBMITTED.getClaimStatus();
                }
            }

            assessorAllocationDao.updateRecordStatusByRefNo(connection, status, motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());

            inspectionDetailsDao.updateRecordStatusByRefNo(connection, status, motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());

            motorEngineerDetailsDao.updateRecordStatusByRefNo(connection, status, motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());

            String rteUser = motorEngineerDetailsDao.getAssignRte(connection, motorEngineerDetailsDto.getAssessorAllocationDto().getJobId());
            inspectionDetailsDao.updateForwardDetails(connection, rteUser, Utility.sysDateTime(), motorEngineerDetailsDto.getInspectionDetailsDto().getRefNo());
            saveNotification(connection, motorEngineerDetailsDto.getClaimNo(), user.getUserId(), rteUser, "You have received returned inspection. ", URL, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
            initialLog(motorEngineerDetailsDto.getInspectionDetailsDto(), connection, user, "Inspection Returned", "Inspection returned by " + user.getUserId() + " to [ " + rteUser + " ]");
            saveSpecialRemark(motorEngineerDetailsDto, user, connection);
            isSuccess = true;
            commitTransaction(connection);
        } catch (Exception ex) {
            rollbackTransaction(connection);
            LOGGER.error(ex.getMessage());
            throw ex;
        } finally {
            releaseJDBCConnection(connection);
        }
        return isSuccess;
    }

    @Override
    public List<ClaimLogTrailDto> getLogDetails(Integer claimNo, Integer jobRefNo) {
        List<ClaimLogTrailDto> loggerTrailList = new ArrayList<>();
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            loggerTrailList = loggerTrailDao.getLoggerTrailForForm(connection, AppConstant.ASSESSOR_AND_MOTOR_LOG, claimNo, jobRefNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return loggerTrailList;
    }

    @Override
    public List<String[]> getRteListWithAuthLevel() throws Exception {
        Connection connection = null;
        List<String[]> usersWithLevels = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            List<String> motorEngineersByAuthLevels = userDao.getMotorEngineersByAuthLevels(connection);

            for (String user : motorEngineersByAuthLevels) {
                String[] users = user.split(",");
                usersWithLevels.add(users);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return usersWithLevels;
    }

    @Override
    public List<PopupItemDto> getRteListForReassign(String userName) throws Exception {
        Connection connection = null;
        List<PopupItemDto> rteListForReassign = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            List<String> rteForReassign = userDao.getRteForReassign(connection, userName);
            String[] userNameValue = rteForReassign.get(0).split(",");
            String[] userNameText = rteForReassign.get(1).split(",");

            for (int i = 0; i < userNameValue.length; i++) {
                PopupItemDto popupItemDto = new PopupItemDto(userNameValue[i], userNameText[i]);
                rteListForReassign.add(popupItemDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return rteListForReassign;
    }

    @Override
    public List<PopupItemDto> getReportingRteListForReassign() throws Exception {
        Connection connection = null;
        List<PopupItemDto> rteListForReassign = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            List<String> rteForReassign = userDao.getRteForReassign(connection);
            String[] userNameValue = rteForReassign.get(0).split(",");
            String[] userNameText = rteForReassign.get(1).split(",");

            for (int i = 0; i < userNameValue.length; i++) {
                PopupItemDto popupItemDto = new PopupItemDto(userNameValue[i], userNameText[i]);
                rteListForReassign.add(popupItemDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return rteListForReassign;
    }

    @Override
    public Integer getClaimStatus(Integer claimNo) throws Exception {
        Connection connection = null;
        Integer claimStatusByClaimNo;
        try {
            connection = getJDBCConnection();
            claimStatusByClaimNo = claimHandlerDao.getClaimStatusByClaimNo(connection, claimNo);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimStatusByClaimNo;
    }

    @Override
    public UserDto getUserDetailByUserId(String userId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return userDao.getUserByUsrid(connection, userId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public ClaimSummaryDto getClaimSummaryDetails(Integer claimNo) {
        Connection connection = null;
        ClaimSummaryDto claimSummaryDto = new ClaimSummaryDto();
        try {
            connection = getJDBCConnection();
            List<InspectionDetailsSummaryDto> inspectionDetailsSummaryDtoList = motorEngineerDetailsDao.getInspectionDetailsSummary(connection, claimNo);
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            if (null != inspectionDetailsSummaryDtoList && !inspectionDetailsSummaryDtoList.isEmpty()) {
                for (InspectionDetailsSummaryDto dto : inspectionDetailsSummaryDtoList) {
                    BigDecimal acr = BigDecimal.ZERO;
                    String consistencyStatus = AppConstant.STRING_EMPTY;
                    if (ClaimStatus.APPROVED.getClaimStatus() == dto.getStatus() || ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus() == dto.getStatus()) {
                        switch (dto.getInspectionId()) {
                            case AppConstant.ON_SITE_INSPECTION://On site Inspection = 1
                            case AppConstant.OFF_SITE_INSPECTION://Off site Inspection = 2
                            case AppConstant.CALL_ESTIMATE://Call Estimate = 11
                                acr = onSiteInspectionDetailsMeDao.getAcr(connection, dto.getRefNo());
                                break;
                            case AppConstant.GARAGE_INSPECTION://Garage Inspection - ACR Reset = 4
                                acr = garageInspectionDetailsMeDao.getAcr(connection, dto.getRefNo());
                                break;
                            case AppConstant.DESKTOP_INSPECTION://Desktop  Inspection - ACR Reset = 8
                                acr = desktopInspectionDetailsMeDao.getAcr(connection, dto.getRefNo());
                                break;
                            case AppConstant.DR_INSPECTION://DR Insepection = 5
                            case AppConstant.SUP_INSPECTION://Supplimantary Inspection = 6
                                acr = drSupplementaryInspectionDetailsMeDao.getAcr(connection, dto.getRefNo());
                                break;
                        }
                        consistencyStatus = inspectionDetailsDao.getConsistencyByRteDetails(connection, dto.getRefNo());
                    } else {
                        switch (dto.getInspectionId()) {
                            case AppConstant.ON_SITE_INSPECTION://On site Inspection = 1
                            case AppConstant.OFF_SITE_INSPECTION://Off site Inspection = 2
                            case AppConstant.CALL_ESTIMATE://Call Estimate = 11
                                acr = onSiteInspectionDetailsDao.getAcr(connection, dto.getRefNo());
                                break;
                            case AppConstant.GARAGE_INSPECTION://Garage Inspection - ACR Reset = 4
                                acr = garageInspectionDetailsDao.getAcr(connection, dto.getRefNo());
                                break;
                            case AppConstant.DR_INSPECTION://DR Insepection = 5
                            case AppConstant.SUP_INSPECTION://Supplimantary Inspection = 6
                                acr = drSupplementaryInspectionDetailsDao.getAcr(connection, dto.getRefNo());
                                break;
                        }
                        consistencyStatus = inspectionDetailsDao.getConsistencyByAssessorDetails(connection, dto.getRefNo());
                    }
                    String consistency = AppConstant.STRING_EMPTY;
                    if ("C".equalsIgnoreCase(consistencyStatus)) {
                        consistency = "Consistent";
                    } else if ("N".equalsIgnoreCase(consistencyStatus)) {
                        consistency = "Non Consistent";
                    } else if ("D".equalsIgnoreCase(consistencyStatus)) {
                        consistency = "Doubtful";
                    }
                    dto.setAcr(acr);
                    dto.setConsistency(consistency);
                }
            }
            claimSummaryDto.setInspectionDetailsSummaryDtoList(inspectionDetailsSummaryDtoList);
            claimSummaryDto.setInitialLiabilityStatus(claimHandlerDto.getInitLiabilityAprvStatus());
            claimSummaryDto.setInitialLiabilityApprovedUser(claimHandlerDto.getInitLiabilityAprvUserId());
            claimSummaryDto.setTotalApprovedAcr(claimHandlerDto.getAprvTotAcrAmount());
            claimSummaryDto.setLiabilityStatus(claimHandlerDto.getLiabilityAprvStatus());
            claimSummaryDto.setLiabilityApprovedUser(claimHandlerDto.getLiabilityAprvUser());

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return claimSummaryDto;
    }

    @Override
    public boolean isOnsiteOrOffSitePending(Integer claimNo) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimHandlerDao.isOnsiteOrOffSitePending(connection, claimNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return false;
    }

    @Override
    public void isValidAuthorize(MotorEngineerDetailsDto motorEngineerDetailsDto, boolean isForward, String emailVal) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();

            // add email part //
            Email email = new Email();
            MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.SALVAGE_INSPECTION);
            email.setEmailMassege(messageContentDetails.getMessageBody());

            ClaimsDto search = callCenterService.getClaimsDtoByClaimNo(connection, motorEngineerDetailsDto.getClaimNo());

            if (null != search) {
                email.setToAddresses(emailVal); // ensure emailVal is declared and available

                ArrayList<String> list = new ArrayList<>();
                // ?1% - Vehicle No
                list.add(search.getVehicleNo() != null ? search.getVehicleNo() : "N/A");
                // ?2% - Job Number
                list.add(motorEngineerDetailsDto.getJobId() != null ? motorEngineerDetailsDto.getJobId() : "N/A");
                // ?3% - Date of Accident
                list.add(search.getAccidDate() != null ? search.getAccidDate() : "N/A");
                // ?4% - Policy No
                list.add(search.getPolicyDto().getPolicyNumber() != null ? search.getPolicyDto().getPolicyNumber() : "N/A");
                // ?5% - Operator Comment
//                list.add(motorEngineerDetailsDto.getInspectionDetailsDto().getOperatorComment() != null ? motorEngineerDetailsDto.getInspectionDetailsDto().getOperatorComment() : "N/A");
                // ?6% - Remarks by Engineering Dept
//                list.add(motorEngineerDetailsDto.getInspectionDetailsDto().getRemarksByEng() != null ? motorEngineerDetailsDto.getInspectionDetailsDto().getRemarksByEng() : "N/A");
                // ?7% - Sender Name
                list.add(getMailSendUser());

                email.setParameterEmail(list);

                String subject = "Salvage Inspection Completed / JOB number - Claim No :- " + search.getClaimNo();
                email.setSubject(subject);

                emailService.sendEmail(connection, email);
            }

            switch (motorEngineerDetailsDto.getAssessorAllocationDto().getInspectionDto().getInspectionId()) {
                case AppConstant.DR_INSPECTION:
                case AppConstant.SUP_INSPECTION:
                    if (motorEngineerDetailsDao.isAvailablePendingInspection(connection, motorEngineerDetailsDto.getClaimNo(), AppConstant.GARAGE_INSPECTION)) {
                        throw new ErrorMsgException(AppConstant.ERROR_MESSAGE, "Record can not ".concat(isForward ? "forward" : "approve") + ". Pending garage inspection review exists for this claim.");
                    } else if (motorEngineerDetailsDao.isAvailablePendingInspection(connection, motorEngineerDetailsDto.getClaimNo(), AppConstant.DESKTOP_INSPECTION)) {
                        throw new ErrorMsgException(AppConstant.ERROR_MESSAGE, "Record can not ".concat(isForward ? "forward" : "approve") + ". Pending desktop inspection review exists for this claim.");
                    }
                    break;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public MotorEngineerDetailsDto getLatestUpdateOnsiteInspectionDetails(MotorEngineerDetailsDto motorEngineerDetailsDto) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            boolean isApprovedInspectionReportDetails = motorEngineerDetailsDao.isApprovedInspectionReportDetails(connection, motorEngineerDetailsDto.getRefNo());

            InspectionDetailsDto latestInspection = inspectionDetailsDao.getLatestInspection(connection, motorEngineerDetailsDto.getClaimNo());
            MotorEngineerDetailsDto latestEngineer = motorEngineerDetailsDao.getLatestInspectionMotorEngineer(connection, motorEngineerDetailsDto.getClaimNo());

            if (null != latestEngineer) {
                OnSiteInspectionDetailsDto onsite = onSiteInspectionDetailsMeDao.searchMaster(connection, latestEngineer.getRefNo());
                motorEngineerDetailsDto.setChassisNo(latestInspection.getChassisNo());
                DesktopInspectionDetailsDto desktopInspectionDetailsDto = motorEngineerDetailsDto.getDesktopInspectionDetailsDto();
                if (null != desktopInspectionDetailsDto) {
                    if (!isApprovedInspectionReportDetails) {
                        motorEngineerDetailsDto.setChassisNo(latestEngineer.getChassisNo());
                        motorEngineerDetailsDto.setPav(latestEngineer.getPav());
                        motorEngineerDetailsDto.setDamageDetails(latestEngineer.getDamageDetails());
                        motorEngineerDetailsDto.setPad(latestEngineer.getPad());
                        motorEngineerDetailsDto.setGenuineOfAccident(latestEngineer.getGenuineOfAccident());
                        motorEngineerDetailsDto.setInvestReq(latestEngineer.getInvestReq());
                        motorEngineerDetailsDto.setEngNoConfirm(latestEngineer.getEngNoConfirm());
                        motorEngineerDetailsDto.getInspectionDetailsDto().setAssessorSpecialRemark(latestInspection.getAssessorSpecialRemark());
                        motorEngineerDetailsDto.setAssessorSpecialRemark(latestEngineer.getAssessorSpecialRemark());
                    }

                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setBoldPercent(onsite.getBoldPercent());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setBoldTyrePenaltyAmount(onsite.getBoldTirePenaltyAmount());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setUnderPenaltyPercent(onsite.getUnderPenaltyPercent());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setUnderInsurancePenaltyAmount(onsite.getUnderPenaltyAmount());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setPayableAmount(onsite.getPayableAmount());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setAcr(onsite.getAcr());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setExcess(onsite.getExcess());

                    motorEngineerDetailsDto.setDesktopInspection(AppConstant.YES);

                    List<TireCondtionDto> tireCondtionDtos = tireCondtionMeDao.searchByClaimNoAndRefNo(connection, motorEngineerDetailsDto.getClaimNo(), latestEngineer.getRefNo());
                    motorEngineerDetailsDto.setTireCondtionDtoList(tireCondtionDtos);

                }
            } else if (null != latestInspection) {
                latestInspection = inspectionDetailsService.search(connection, latestInspection.getRefNo());
                motorEngineerDetailsDto.setChassisNo(latestInspection.getChassisNo());
                DesktopInspectionDetailsDto desktopInspectionDetailsDto = motorEngineerDetailsDto.getDesktopInspectionDetailsDto();
                if (null != desktopInspectionDetailsDto) {
                    if (!isApprovedInspectionReportDetails) {
                        motorEngineerDetailsDto.setChassisNo(latestInspection.getChassisNo());
                        motorEngineerDetailsDto.setPav(latestInspection.getPav());
                        motorEngineerDetailsDto.setDamageDetails(latestInspection.getDamageDetails());
                        motorEngineerDetailsDto.setPad(latestInspection.getPad());
                        motorEngineerDetailsDto.setGenuineOfAccident(latestInspection.getGenuineOfAccident());
                        motorEngineerDetailsDto.setInvestReq(latestInspection.getInvestReq());
                        motorEngineerDetailsDto.setEngNoConfirm(latestInspection.getEngNoConfirm());
                        motorEngineerDetailsDto.getInspectionDetailsDto().setAssessorSpecialRemark(latestInspection.getAssessorSpecialRemark());
                        motorEngineerDetailsDto.setAssessorSpecialRemark(AppConstant.STRING_EMPTY);
                    }

                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setBoldPercent(latestInspection.getOnSiteInspectionDetailsDto().getBoldPercent());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setBoldTyrePenaltyAmount(latestInspection.getOnSiteInspectionDetailsDto().getBoldTirePenaltyAmount());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setUnderPenaltyPercent(latestInspection.getOnSiteInspectionDetailsDto().getUnderPenaltyPercent());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setUnderInsurancePenaltyAmount(latestInspection.getOnSiteInspectionDetailsDto().getUnderPenaltyAmount());
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setPayableAmount(latestInspection.getOnSiteInspectionDetailsDto().getPayableAmount());
                    motorEngineerDetailsDto.setDesktopInspection(AppConstant.YES);

                    List<TireCondtionDto> tireCondtionDtos = tireCondtionMeDao.searchByClaimNoAndRefNo(connection, motorEngineerDetailsDto.getClaimNo(), latestInspection.getRefNo());
                    motorEngineerDetailsDto.setTireCondtionDtoList(tireCondtionDtos);

                }
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return motorEngineerDetailsDto;
    }

    public String before(String value, String a) {
        // Return substring containing all characters before a string.
        int posA = value.indexOf(a);
        if (posA == -1) {
            return "";
        }
        return value.substring(0, posA);
    }

    public String after(String value, String a) {
        // Returns a substring containing all characters after a string.
        int posA = value.lastIndexOf(a);
        if (posA == -1) {
            return "";
        }
        int adjustedPosA = posA + a.length();
        if (adjustedPosA >= value.length()) {
            return "";
        }
        return value.substring(adjustedPosA);
    }

    public List<String> getSelectedList(String regex, String array) {
        List<String> list = null;
        if (!array.isEmpty()) {
            array = array.replaceAll(regex, "");
            list = Arrays.stream(array.split(",")).map(String::toString).collect(Collectors.toList());
        } else {
            list = new ArrayList<>();
        }

        return list;
    }

    private boolean isPavAndSumInsuredGreaterThanAcr(BigDecimal sumInsured, BigDecimal pav, BigDecimal totalAcr) {
        return (totalAcr.compareTo(sumInsured) <= 0 && totalAcr.compareTo(pav) <= 0);
    }

    private boolean isSumInsuredGreaterThanAcr(BigDecimal sumInsured, BigDecimal totalAcr) {
        return (totalAcr.compareTo(sumInsured) <= 0);
    }

    private boolean isValidUserAllocatedInspectionType(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, int offerOrLossType) throws Exception {
        boolean result = false;
        boolean isGarage = motorEngineerDetailsDto.getInspectionDto().getInspectionId() == 4;
        boolean isOnsite = motorEngineerDetailsDto.getInspectionDto().getInspectionId() == 1;
        boolean isOffSite = motorEngineerDetailsDto.getInspectionDto().getInspectionId() == 2;
        try {
            boolean isAvailableOnsiteOrOffsite = claimHandlerDao.isAvailableOnsiteOrOffsite(connection, motorEngineerDetailsDto.getClaimNo());
            boolean isOnsiteOrOffSitePending = claimHandlerDao.isOnsiteOrOffSitePending(connection, motorEngineerDetailsDto.getClaimNo());
            boolean isVehicleNotAvailableOnSiteOffSite = inspectionDetailsDao.getIsVehicleNotAvailableOnSiteOffSite(connection, motorEngineerDetailsDto.getClaimNo());

            if (isGarage && offerOrLossType == AppConstant.TOTAL_LOSS_TYPE && isOnsiteOrOffSitePending) {
                throw new ErrorMsgException(AppConstant.ERROR_MESSAGE, "Record can not submit. Pending On-site / Off-site inspection review exists for this claim.");
            }

            if ((isAvailableOnsiteOrOffsite && (isOnsite || isOffSite)) || (!isAvailableOnsiteOrOffsite && isGarage) || (isVehicleNotAvailableOnSiteOffSite && isGarage)) {
                result = true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return result;
    }

    private int getOfferOrLossType(MotorEngineerDetailsDto motorEngineerDetailsDto, int lossType) {
        switch (motorEngineerDetailsDto.getInspectionDetailsDto().getInspectionDto().getInspectionId()) {
            case AppConstant.GARAGE_INSPECTION:
                return getGarageOfferType(motorEngineerDetailsDto);
            case AppConstant.DESKTOP_INSPECTION:
                return getDesktopOfferType(motorEngineerDetailsDto);
            case AppConstant.ON_SITE_INSPECTION:
            case AppConstant.OFF_SITE_INSPECTION:
                return getOnsiteOfferType(motorEngineerDetailsDto);
        }
        return lossType;
    }

    private int getGarageOfferType(MotorEngineerDetailsDto motorEngineerDetailsDto) {
        if (Integer.parseInt(motorEngineerDetailsDto.getGarageInspectionDetailsDto().getSettlementMethod()) == AppConstant.TOTAL_LOSS_TYPE) {
            return AppConstant.TOTAL_LOSS_TYPE;
        } else if (Integer.parseInt(motorEngineerDetailsDto.getGarageInspectionDetailsDto().getSettlementMethod()) == AppConstant.GARAGE_OFFER_TYPE) {
            return AppConstant.GARAGE_OFFER_TYPE;
        }
        return AppConstant.PARTIAL_LOSS_TYPE;
    }

    private int getOnsiteOfferType(MotorEngineerDetailsDto motorEngineerDetailsDto) {
        if (4 == motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getOfferType()) {
            return AppConstant.TOTAL_LOSS_TYPE;
        } else if (1 == motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getOfferType()) {
            return AppConstant.ONSITE_OFFER_TYPE;
        }
        return AppConstant.PARTIAL_LOSS_TYPE;
    }

    private int getDesktopOfferType(MotorEngineerDetailsDto motorEngineerDetailsDto) {
        if (Integer.parseInt(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getSettlementMethod()) == AppConstant.TOTAL_LOSS_TYPE) {
            return AppConstant.TOTAL_LOSS_TYPE;
        }
        if (motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getDesktopOffer() == ConditionType.Yes) {
            return AppConstant.DESKTOP_OFFER_TYPE;
        }
        return AppConstant.PARTIAL_LOSS_TYPE;
    }

    private void updateClaimHandler(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user, boolean isChangeRequest, boolean isAlreadySentChangeRequest, BigDecimal premium, boolean isCancelled) throws Exception {
        String url = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(motorEngineerDetailsDto.getClaimNo())).concat("&P_TAB_INDEX=0");
        try {
            ClaimHandlerDto searchedClaimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, motorEngineerDetailsDto.getClaimNo());
            int offerOrLossType = this.getOfferOrLossType(motorEngineerDetailsDto, searchedClaimHandlerDto.getLossType());

            if (motorEngineerDetailsDto.getInspectionDetailsDto().getInspectionDto().getInspectionId() == AppConstant.GARAGE_INSPECTION || motorEngineerDetailsDto.getInspectionDetailsDto().getInspectionDto().getInspectionId() == AppConstant.DESKTOP_INSPECTION || motorEngineerDetailsDto.getInspectionDetailsDto().getInspectionDto().getInspectionId() == AppConstant.ON_SITE_INSPECTION || motorEngineerDetailsDto.getInspectionDetailsDto().getInspectionDto().getInspectionId() == AppConstant.OFF_SITE_INSPECTION) {
                this.updateLossType(connection, motorEngineerDetailsDto, searchedClaimHandlerDto, user, offerOrLossType, url, isChangeRequest, isAlreadySentChangeRequest);
            } else if (motorEngineerDetailsDto.getInspectionDetailsDto().getRecordStatus() == ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus() || isAlreadySentChangeRequest) {
                if (isChangeRequest) {
                    sendNotificationForReplyChangeRequest(connection, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo(), searchedClaimHandlerDto.getClaimNo(), url, user.getUserId());
                }
            }
            if (isAlreadySentChangeRequest) {
                updateChangeRequestDetails(connection, motorEngineerDetailsDto, user);
            }

            updateTotalApprovedAcr(connection, motorEngineerDetailsDto, searchedClaimHandlerDto, offerOrLossType, isAlreadySentChangeRequest, user);
            saveConfirmationLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, premium, isCancelled);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }

    }

    private void updateChangeRequestDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) throws Exception {
        try {
            ChangeRequestDetailDto changeRequestDetailDto = changeRequestDetailDao.searchByRefNo(connection, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());

            if (null != changeRequestDetailDto) {
                changeRequestDetailDto.setResponseUser(user.getUserId());
                changeRequestDetailDto.setResponseDatetime(Utility.sysDateTime());
                changeRequestDetailDto.setResponseType(AppConstant.APPROVE);
                changeRequestDetailDao.update(connection, changeRequestDetailDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateLossType(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, ClaimHandlerDto searchedClaimHandlerDto, UserDto user, int offerOrLossType, String url, boolean isChangeRequest, boolean isAlreadySentChangeRequest) throws Exception {

        try {
            boolean isInitialLiabilityAssigned = (null != searchedClaimHandlerDto.getInitLiabilityAssignUserId() && !searchedClaimHandlerDto.getInitLiabilityAssignUserId().equals(AppConstant.STRING_EMPTY));

            if (isInitialLiabilityAssigned) {
                reassignTeam(connection, motorEngineerDetailsDto, searchedClaimHandlerDto, user, offerOrLossType, url, isChangeRequest, isAlreadySentChangeRequest);
            } else if (isValidUserAllocatedInspectionType(connection, motorEngineerDetailsDto, offerOrLossType)) {
                assignTeam(connection, motorEngineerDetailsDto, searchedClaimHandlerDto, user, offerOrLossType, url);
            }
//            updateLossTypeAndIsProvideOffer(connection, motorEngineerDetailsDto.getClaimNo(), offerOrLossType);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void assignTeam(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, ClaimHandlerDto searchedClaimHandlerDto, UserDto user, int offerOrLossType, String url) throws Exception {
        try {
            if (motorEngineerDetailsDto.getInspectionDto().getInspectionId() == AppConstant.ON_SITE_INSPECTION || motorEngineerDetailsDto.getInspectionDto().getInspectionId() == AppConstant.OFF_SITE_INSPECTION) {
                assignTeamAfterApprovedOnsiteOrOffsite(connection, motorEngineerDetailsDto, user, offerOrLossType, url);
            } else {
                assignTeamAfterApprovedGarage(connection, motorEngineerDetailsDto, user, offerOrLossType, url);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void assignTeamAfterApprovedGarage(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user, int offerOrLossType, String url) throws Exception {
        if (offerOrLossType == AppConstant.TOTAL_LOSS_TYPE) {
            assignTotalLossInitLiabilityAndClaimHandler(connection, motorEngineerDetailsDto, url, user);
        } else if (offerOrLossType == AppConstant.GARAGE_OFFER_TYPE) {
            assignOfferTeamPartialLossInitLiability(connection, motorEngineerDetailsDto, offerOrLossType, url, user);
        } else {
            assignPartialLossInitLiability(connection, motorEngineerDetailsDto, url, user);
        }
        updateLossTypeAndIsProvideOffer(connection, motorEngineerDetailsDto.getClaimNo(), offerOrLossType);
    }

    private void assignTeamAfterApprovedOnsiteOrOffsite(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user, int offerOrLossType, String url) throws Exception {
        if (offerOrLossType == AppConstant.TOTAL_LOSS_TYPE) {
            assignTotalLossInitLiabilityAndClaimHandler(connection, motorEngineerDetailsDto, url, user);
        } else if (offerOrLossType == AppConstant.ONSITE_OFFER_TYPE) {
            assignOfferTeamPartialLossInitLiability(connection, motorEngineerDetailsDto, offerOrLossType, url, user);
        } else {
            assignPartialLossInitLiability(connection, motorEngineerDetailsDto, url, user);
        }
        updateLossTypeAndIsProvideOffer(connection, motorEngineerDetailsDto.getClaimNo(), offerOrLossType);
    }

    private void reassignTeam(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, ClaimHandlerDto searchedClaimHandlerDto, UserDto user, int offerOrLossType, String url, boolean isChangeRequest, boolean isAlreadySentChangeRequest) throws Exception {
        try {
            if (isAssignTotalLossTeam(searchedClaimHandlerDto, offerOrLossType)) {
                reassignAsTotalLossTeam(connection, motorEngineerDetailsDto, searchedClaimHandlerDto, url, user);
                updateLossTypeAndIsProvideOffer(connection, motorEngineerDetailsDto.getClaimNo(), offerOrLossType);
            } else if (isAssignOfferTeam(motorEngineerDetailsDto, searchedClaimHandlerDto, offerOrLossType, isAlreadySentChangeRequest)) {
                reassignAsOfferTeam(connection, motorEngineerDetailsDto, searchedClaimHandlerDto, offerOrLossType, url, user);
                updateLossTypeAndIsProvideOffer(connection, motorEngineerDetailsDto.getClaimNo(), offerOrLossType);
            } else if (isAssignPartialLossTeam(searchedClaimHandlerDto, offerOrLossType)) {
                reassignAsPartialLossTeam(connection, motorEngineerDetailsDto, searchedClaimHandlerDto, url, user);
                updateLossTypeAndIsProvideOffer(connection, motorEngineerDetailsDto.getClaimNo(), offerOrLossType);
            } else if (motorEngineerDetailsDto.getInspectionDetailsDto().getRecordStatus() == ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus() || isAlreadySentChangeRequest) {
                if (isChangeRequest) {
                    sendNotificationForReplyChangeRequest(connection, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo(), searchedClaimHandlerDto.getClaimNo(), url, user.getUserId());
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateLossTypeAndIsProvideOffer(Connection connection, int claimNo, int offerOrLossType) throws Exception {
        try {
            int lossType;
            String isProvideOffer;
            if (offerOrLossType == AppConstant.TOTAL_LOSS_TYPE) {
                isProvideOffer = AppConstant.NO;
                lossType = AppConstant.TOTAL_LOSS_TYPE;
            } else if ((offerOrLossType == AppConstant.ONSITE_OFFER_TYPE || offerOrLossType == AppConstant.GARAGE_OFFER_TYPE || offerOrLossType == AppConstant.DESKTOP_OFFER_TYPE)) {
                isProvideOffer = AppConstant.YES;
                lossType = AppConstant.PARTIAL_LOSS_TYPE;
            } else {
                isProvideOffer = AppConstant.NO;
                lossType = AppConstant.PARTIAL_LOSS_TYPE;

            }
            claimHandlerDao.updateIsProvideOffer(connection, claimNo, isProvideOffer);
            claimHandlerDao.updateLossType(connection, claimNo, String.valueOf(lossType));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private boolean isAssignTotalLossTeam(ClaimHandlerDto searchedClaimHandlerDto, int offerOrLossType) {
        return offerOrLossType == AppConstant.TOTAL_LOSS_TYPE && !(searchedClaimHandlerDto.getLossType().equals(AppConstant.TOTAL_LOSS_TYPE));
    }

    private boolean isAssignPartialLossTeam(ClaimHandlerDto searchedClaimHandlerDto, int offerOrLossType) {
        return (offerOrLossType == AppConstant.PARTIAL_LOSS_TYPE) && (!searchedClaimHandlerDto.getLossType().equals(AppConstant.PARTIAL_LOSS_TYPE) || searchedClaimHandlerDto.getIsProvideOffer().equalsIgnoreCase(AppConstant.YES));
    }

    private boolean isAssignOfferTeam(MotorEngineerDetailsDto motorEngineerDetailsDto, ClaimHandlerDto searchedClaimHandlerDto, int offerOrLossType, boolean isAlreadySentChangeRequest) {
        return ((searchedClaimHandlerDto.getIsProvideOffer().equalsIgnoreCase(AppConstant.NO)) && (offerOrLossType == AppConstant.ONSITE_OFFER_TYPE || offerOrLossType == AppConstant.GARAGE_OFFER_TYPE || offerOrLossType == AppConstant.DESKTOP_OFFER_TYPE)) && (motorEngineerDetailsDto.getInspectionDetailsDto().getRecordStatus() != ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus() && !isAlreadySentChangeRequest);
    }

    private void sendNotificationForReplyChangeRequest(Connection connection, int refNo, Integer claimNo, String url, String userId) throws Exception {
        try {
            ChangeRequestDetailDto changeRequestDetailDto = changeRequestDetailDao.searchByRefNo(connection, refNo);

            if (null != changeRequestDetailDto) {
                saveNotification(connection, claimNo, userId, changeRequestDetailDto.getRequestUser(), "You have received reply for change request.", url);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void createOfferCalSheet(Connection connection, ClaimHandlerDto claimHandlerDto, Integer calSheetType, BigDecimal acr, BigDecimal underPenaltyPercent, BigDecimal underPenaltyAmount, BigDecimal boldPercent, BigDecimal boldTirePenaltyAmount, BigDecimal costPart, BigDecimal costLabour, Integer payeeId, String payeeDesc, Integer causeOfLoss, UserDto user) throws Exception {
        ClaimCalculationSheetMainDto claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();

        String userId = claimHandlerDto.getAssignUserId();
        if (null == userId || userId.isEmpty()) {
            userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimHandlerDto.getClaimNo());

            claimHandlerDto.setAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setAssignStatus("P");
            claimHandlerDto.setAssignUserId(userId);
            claimHandlerDao.updateAssignUser(connection, claimHandlerDto);
        }


        claimCalculationSheetMainDto.setInputUser(userId);
        claimCalculationSheetMainDto.setInputDatetime(Utility.sysDateTime());
        claimCalculationSheetMainDto.setAssignUserId(userId);
        claimCalculationSheetMainDto.setAssignDateTime(Utility.sysDateTime());
        claimCalculationSheetMainDto.setClaimNo(claimHandlerDto.getClaimNo());
        claimCalculationSheetMainDto.setLossType(AppConstant.PARTIAL_LOSS_TYPE);
        claimCalculationSheetMainDto.setCauseOfLoss(causeOfLoss);// need to check
        claimCalculationSheetMainDto.setPaymentType(AppConstant.OFFER_PAYMENT_TYPE);
        claimCalculationSheetMainDto.setCalSheetType(calSheetType);
        claimCalculationSheetMainDto.setLabour(BigDecimal.ZERO);
        claimCalculationSheetMainDto.setParts(acr);
        claimCalculationSheetMainDto.setTotalLabourAndParts(acr);
        claimCalculationSheetMainDto.setPolicyExcess(claimHandlerDto.getClaimsDto().getPolicyDto().getExcess());
        claimCalculationSheetMainDto.setUnderInsuranceRate(underPenaltyPercent);
        claimCalculationSheetMainDto.setUnderInsurance(underPenaltyAmount);
        claimCalculationSheetMainDto.setBaldTyreRate(boldPercent);
        claimCalculationSheetMainDto.setBaldTyre(boldTirePenaltyAmount);
        claimCalculationSheetMainDto.setSpecialDeductions(BigDecimal.ZERO);
        claimCalculationSheetMainDto.setSpecialVatAmount(BigDecimal.ZERO);
        claimCalculationSheetMainDto.setSpecialNbtAmount(BigDecimal.ZERO);
        claimCalculationSheetMainDto.setTotalDeductions(BigDecimal.ZERO);
        claimCalculationSheetMainDto.setTotalAfterDeductions(acr);
        claimCalculationSheetMainDto.setPaidAdvanceAmount(BigDecimal.ZERO);
        claimCalculationSheetMainDto.setPayableAmount(acr);
        claimCalculationSheetMainDto.setStatus(58);

//
//        List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailReplacementDtos = claimCalculationSheetMainDto.getClaimCalculationSheetDetailReplacementDtos();
//        List<ClaimCalculationSheetPayeeDto> claimCalculationSheetPayeeDtos = claimCalculationSheetMainDto.getClaimCalculationSheetPayeeDtos();


        ClaimCalculationSheetMainDto insertMasterCalculationSheetMainDto = claimCalculationSheetMainDao.insertMaster(connection, claimCalculationSheetMainDto);

        String calSheetDesc = dbRecordCommonFunction.getValue("claim_calculation_sheet_type", "V_CAL_SHEET_TYPE_DESC", "N_CAL_SHEET_TYPE_ID", String.valueOf(claimCalculationSheetMainDto.getCalSheetType()));

        saveClaimsLogs(connection, claimCalculationSheetMainDto.getClaimNo(), user, String.format("%s Calculation Sheet Saved", calSheetDesc), "Calculation Sheet Saved");
        this.saveCalculationProcessFlow(connection, claimCalculationSheetMainDto.getClaimNo(), insertMasterCalculationSheetMainDto.getCalSheetId(), 58, String.format("%s Calculation Sheet Created", calSheetDesc), user.getUserId(), AppConstant.STRING_EMPTY);
        ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = claimCalculationSheetTypeDao.searchMaster(connection, insertMasterCalculationSheetMainDto.getCalSheetType());

        String URL = AppConstant.MOTORENG_CAL_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(insertMasterCalculationSheetMainDto.getClaimNo())).concat("&P_CAL_SHEET_NO=").concat(String.valueOf(insertMasterCalculationSheetMainDto.getCalSheetId())).concat("&TYPE=7").concat("&P_TAB_INDEX=").concat(String.valueOf(15));

//        saveNotification(connection, insertMasterCalculationSheetMainDto.getClaimNo(), user.getUserId(), userId, "You have received a cal sheet from " + user.getUserId() + " for recommendation".concat(" Cal Sheet No - ".concat(String.valueOf(insertMasterCalculationSheetMainDto.getCalSheetId()))), URL);


        ClaimCalculationSheetDetailDto claimCalculationSheetDetailReplacementDto = new ClaimCalculationSheetDetailDto();

        claimCalculationSheetDetailReplacementDto.setCalSheetId(insertMasterCalculationSheetMainDto.getCalSheetId());
        claimCalculationSheetDetailReplacementDto.setItemNo(1);
        claimCalculationSheetDetailReplacementDto.setRecordType("1"); //Replacement
        claimCalculationSheetDetailReplacementDto.setApprovedAmount(costPart);
        claimCalculationSheetDetailReplacementDto.setNbtType("IGNORE");
        claimCalculationSheetDetailReplacementDto.setNbtAmount(BigDecimal.ZERO);
        claimCalculationSheetDetailReplacementDto.setVatType("IGNORE");
        claimCalculationSheetDetailReplacementDto.setVatAmount(BigDecimal.ZERO);
        claimCalculationSheetDetailReplacementDto.setOaAmount(BigDecimal.ZERO);
        claimCalculationSheetDetailReplacementDto.setBillChecked(AppConstant.NO);
        claimCalculationSheetDetailReplacementDto.setIsRemoveVat("N");
        claimCalculationSheetDetailReplacementDto.setIsAddNbt("N");
        claimCalculationSheetDetailReplacementDto.setAddVatType("WITHOUTNBT");
        claimCalculationSheetDetailDao.insertMaster(connection, claimCalculationSheetDetailReplacementDto);

        ClaimCalculationSheetDetailDto claimCalculationSheetDetailLabourDto = new ClaimCalculationSheetDetailDto();
        claimCalculationSheetDetailLabourDto.setCalSheetId(insertMasterCalculationSheetMainDto.getCalSheetId());
        claimCalculationSheetDetailLabourDto.setRecordType("2"); //LABOUR
        claimCalculationSheetDetailLabourDto.setItemNo(1);
        claimCalculationSheetDetailLabourDto.setApprovedAmount(costLabour);
        claimCalculationSheetDetailLabourDto.setNbtType("IGNORE");
        claimCalculationSheetDetailLabourDto.setNbtAmount(BigDecimal.ZERO);
        claimCalculationSheetDetailLabourDto.setVatType("IGNORE");
        claimCalculationSheetDetailLabourDto.setVatAmount(BigDecimal.ZERO);
        claimCalculationSheetDetailLabourDto.setOaAmount(BigDecimal.ZERO);
        claimCalculationSheetDetailLabourDto.setBillChecked(AppConstant.NO);
        claimCalculationSheetDetailLabourDto.setIsRemoveVat("N");
        claimCalculationSheetDetailLabourDto.setIsAddNbt("N");
        claimCalculationSheetDetailLabourDto.setAddVatType("WITHOUTNBT");
        claimCalculationSheetDetailDao.insertMaster(connection, claimCalculationSheetDetailLabourDto);

//        for (ClaimCalculationSheetDetailDto claimCalculationSheetDetailReplacementDto : claimCalculationSheetDetailReplacementDtos) {
//            claimCalculationSheetDetailReplacementDto.setCalSheetId(insertMasterCalculationSheetMainDto.getCalSheetId());
//            claimCalculationSheetDetailReplacementDto.setRecordType("1"); //Replacement
//            claimCalculationSheetDetailReplacementDto.setApprovedAmount(motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr());
//            claimCalculationSheetDetailReplacementDto.setNbtType("IGNORE");
//            claimCalculationSheetDetailReplacementDto.setNbtAmount(BigDecimal.ZERO);
//            claimCalculationSheetDetailReplacementDto.setVatType("IGNORE");
//            claimCalculationSheetDetailReplacementDto.setVatAmount(BigDecimal.ZERO);
//            claimCalculationSheetDetailReplacementDto.setOaAmount(BigDecimal.ZERO);
//            claimCalculationSheetDetailReplacementDto.setBillChecked(AppConstant.NO);
//            claimCalculationSheetDetailReplacementDto.setRecordType("1");
//            claimCalculationSheetDetailReplacementDto.setIsRemoveVat("N");
//            claimCalculationSheetDetailReplacementDto.setIsAddNbt("N");
//            claimCalculationSheetDetailReplacementDto.setAddVatType("WITHOUTNBT");
//            claimCalculationSheetDetailDao.insertMaster(connection, claimCalculationSheetDetailReplacementDto);
//        }


//        for (ClaimCalculationSheetDetailDto claimCalculationSheetDetailLabourDto : claimCalculationSheetDetailLabourDtos) {
//            claimCalculationSheetDetailLabourDto.setCalSheetId(insertMasterCalculationSheetMainDto.getCalSheetId());
//            claimCalculationSheetDetailLabourDto.setRecordType("2"); //LABOUR
//            claimCalculationSheetDetailDao.insertMaster(connection, claimCalculationSheetDetailLabourDto);
//        }


        ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto = new ClaimCalculationSheetPayeeDto();

        claimCalculationSheetPayeeDto.setCalSheetId(insertMasterCalculationSheetMainDto.getCalSheetId());
        claimCalculationSheetPayeeDto.setPayeeId(payeeId);
        claimCalculationSheetPayeeDto.setPayeeDesc(payeeDesc);
        claimCalculationSheetPayeeDto.setAmount(acr);
        claimCalculationSheetPayeeDto.setEmailStatus(AppConstant.NO);
        claimCalculationSheetPayeeDto.setResponseDateTime(AppConstant.DEFAULT_DATE_TIME);
        claimCalculationSheetPayeeDto.setChequeStatus(AppConstant.STRING_PENDING);
        ClaimCalculationSheetPayeeDto generatedPayee = claimCalculationSheetPayeeDao.insertMaster(connection, claimCalculationSheetPayeeDto);

        ClaimPaymentDispatchDto paymentDispatchDto = new ClaimPaymentDispatchDto();
        paymentDispatchDto.setPayeeId(payeeId);
        paymentDispatchDto.setCalSheetId(insertMasterCalculationSheetMainDto.getCalSheetId());
        paymentDispatchDto.setClaimNo(claimCalculationSheetMainDto.getClaimNo());
        paymentDispatchDto.setDispatchLocation("HEAD OFFICE");
        claimPaymentDispatchDao.savePaymentDispatch(connection, paymentDispatchDto);

//        for (ClaimCalculationSheetPayeeDto claimCalculationSheetPayeeDto : claimCalculationSheetPayeeDtos) {
//            claimCalculationSheetPayeeDto.setCalSheetId(insertMasterCalculationSheetMainDto.getCalSheetId());
//            claimCalculationSheetPayeeDto.setPayeeId(2);
//            claimCalculationSheetPayeeDto.setPayeeDesc("AAAA");
//            claimCalculationSheetPayeeDto.setAmount(motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr());
//            claimCalculationSheetPayeeDto.setEmailStatus(AppConstant.NO);
//            claimCalculationSheetPayeeDto.setResponseDateTime(AppConstant.DEFAULT_DATE_TIME);
//            claimCalculationSheetPayeeDto.setChequeStatus(AppConstant.STRING_PENDING);
//            ClaimCalculationSheetPayeeDto generatedPayee = claimCalculationSheetPayeeDao.insertMaster(connection, claimCalculationSheetPayeeDto);
//
//            ClaimPaymentDispatchDto paymentDispatchDto = new ClaimPaymentDispatchDto();
//            paymentDispatchDto.setPayeeId(generatedPayee.getCalSheetPayeeId());
//            paymentDispatchDto.setCalSheetId(calSheetId);
//            paymentDispatchDto.setClaimNo(claimCalculationSheetMainDto.getClaimNo());
//            paymentDispatchDto.setDispatchLocation(claimCalculationSheetPayeeDto.getBranchDetailDto().getBranchCode());
//            claimPaymentDispatchDao.savePaymentDispatch(connection, paymentDispatchDto);
//        }

    }

    private void saveCalculationProcessFlow(Connection connection, Integer claimNo, Integer calSheetId, Integer calSheetStatus, String task, String userId, String assignUserId) throws Exception {
        try {
            CalculationProcessFlowDto calculationProcessFlowDto = new CalculationProcessFlowDto();
            calculationProcessFlowDto.setClaimNo(claimNo);
            calculationProcessFlowDto.setCalSheetId(calSheetId);
            calculationProcessFlowDto.setCalSheetStatus(calSheetStatus);
            calculationProcessFlowDto.setInpUserId(userId);
            calculationProcessFlowDto.setInpDateTime(Utility.sysDateTime());
            calculationProcessFlowDto.setTask(task);
            calculationProcessFlowDto.setAssignUserId(assignUserId);
            calculationProcessFlowDao.insert(connection, calculationProcessFlowDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateTotalApprovedAcr(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, ClaimHandlerDto searchedClaimHandlerDto, int lossType, boolean isAlreadySentChangeRequest, UserDto user) throws Exception {
        try {
            int inspectionTypeId = motorEngineerDetailsDto.getInspectionDto().getInspectionId();

            boolean isAvailableApprovedGarageInspection = motorEngineerDetailsDao.isAvailableApprovedInspectionByInspectionType(connection, searchedClaimHandlerDto.getClaimNo(), AppConstant.GARAGE_INSPECTION);
            boolean isAvailableApprovedDesktopInspection = motorEngineerDetailsDao.isAvailableApprovedInspectionByInspectionType(connection, searchedClaimHandlerDto.getClaimNo(), AppConstant.DESKTOP_INSPECTION);
            boolean isAvailableApprovedDrInspection = motorEngineerDetailsDao.isAvailableApprovedInspectionByInspectionType(connection, searchedClaimHandlerDto.getClaimNo(), AppConstant.DR_INSPECTION);
            boolean isAvailableApprovedSupplimantaryInspection = motorEngineerDetailsDao.isAvailableApprovedInspectionByInspectionType(connection, searchedClaimHandlerDto.getClaimNo(), AppConstant.SUP_INSPECTION);


            switch (inspectionTypeId) {
                case AppConstant.ON_SITE_INSPECTION://On site Inspection = 1
                    if (AppConstant.OFFER_TYPE_ONSITE_OFFER == motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getOfferType() || AppConstant.OFFER_TYPE_ONSITE_CLAIMEE == motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getOfferType()) {
                        this.createOfferCalSheet(connection, searchedClaimHandlerDto, AppConstant.OFFER_TYPE_ONSITE_OFFER == motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getOfferType() ? AppConstant.CAL_SHEET_TYPE_ON_SITE_OFFER : AppConstant.CAL_SHEET_TYPE_ON_SITE_CLAIMEE_OFFER, motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr(), motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getUnderPenaltyPercent(), motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getUnderPenaltyAmount(), motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getBoldPercent(), motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getBoldTirePenaltyAmount(), motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getCostPart(), motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getCostLabour(), 1, null != searchedClaimHandlerDto.getClaimsDto().getPolicyDto().getCustName() ? searchedClaimHandlerDto.getClaimsDto().getPolicyDto().getCustName() : AppConstant.NOT_AVAILABLE, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getCauseOfLoss(), user);
                    }
                case AppConstant.OFF_SITE_INSPECTION://Off site Inspection = 2
                case AppConstant.CALL_ESTIMATE://Call Estimate = 11
                    if (!isAvailableApprovedGarageInspection && !isAvailableApprovedDesktopInspection && !isAvailableApprovedDrInspection && !isAvailableApprovedSupplimantaryInspection) {
                        updateTotalApprovedAcrByOnsiteInspection(connection, motorEngineerDetailsDto, searchedClaimHandlerDto, isAlreadySentChangeRequest, user);
                    }
                    updatePenaltyAmount(connection, motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getBoldPercent(), motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getBoldTirePenaltyAmount(), motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getUnderPenaltyPercent(), motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getUnderPenaltyAmount(), searchedClaimHandlerDto);

                    claimHandlerDao.updateLabourCostAndPartCostAmount(connection, motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getCostLabour(), motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getCostPart());
                    break;

                case AppConstant.GARAGE_INSPECTION://Garage Inspection - ACR Reset = 4

                    if (AppConstant.SETTLEMENT_METHOD_GARAGE_OFFER.equals(motorEngineerDetailsDto.getGarageInspectionDetailsDto().getSettlementMethod())) {
                        this.createOfferCalSheet(connection, searchedClaimHandlerDto, AppConstant.CAL_SHEET_TYPE_GARAGE_OFFER, motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr(), motorEngineerDetailsDto.getGarageInspectionDetailsDto().getUnderPenaltyPercent(), motorEngineerDetailsDto.getGarageInspectionDetailsDto().getUnderInsurancePenaltyAmount(), motorEngineerDetailsDto.getGarageInspectionDetailsDto().getBoldPercent(), motorEngineerDetailsDto.getGarageInspectionDetailsDto().getBoldTyrePenaltyAmount(), motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr(), BigDecimal.ZERO, 2, null != searchedClaimHandlerDto.getClaimsDto().getPolicyDto().getCustName() ? searchedClaimHandlerDto.getClaimsDto().getPolicyDto().getCustName() : AppConstant.NOT_AVAILABLE, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getCauseOfLoss(), user);
                    }

                    updateTotalApprovedAcrByGarageInspection(connection, motorEngineerDetailsDto, searchedClaimHandlerDto, isAlreadySentChangeRequest, user);
                    OnSiteInspectionDetailsDto latestPaneltyRate = onSiteInspectionDetailsMeDao.getLatestPaneltyRate(connection, searchedClaimHandlerDto.getClaimNo());
                    updatePenaltyAmount(connection, latestPaneltyRate.getBoldPercent(), motorEngineerDetailsDto.getGarageInspectionDetailsDto().getBoldTyrePenaltyAmount(), latestPaneltyRate.getUnderPenaltyPercent(), motorEngineerDetailsDto.getGarageInspectionDetailsDto().getUnderInsurancePenaltyAmount(), searchedClaimHandlerDto);

                    if (AppConstant.GARAGE_OFFER_TYPE != lossType && !searchedClaimHandlerDto.getLossType().equals(lossType)) {
                        updateDefineDocument(connection, motorEngineerDetailsDto, lossType, user);
                    }
                    break;

                case AppConstant.DESKTOP_INSPECTION://Desktop  Inspection - ACR Reset = 8

                    if (AppConstant.SETTLEMENT_METHOD_GARAGE_OFFER.equals(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getSettlementMethod())) {
                        this.createOfferCalSheet(connection, searchedClaimHandlerDto, AppConstant.CAL_SHEET_TYPE_GARAGE_OFFER, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAcr(), motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getUnderPenaltyPercent(), motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getUnderInsurancePenaltyAmount(), motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getBoldPercent(), motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getBoldTyrePenaltyAmount(), motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr(), BigDecimal.ZERO, 2, null != searchedClaimHandlerDto.getClaimsDto().getPolicyDto().getCustName() ? searchedClaimHandlerDto.getClaimsDto().getPolicyDto().getCustName() : AppConstant.NOT_AVAILABLE, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getCauseOfLoss(), user);
                    }

                    updateTotalApprovedAcrByDesktopInspection(connection, motorEngineerDetailsDto, searchedClaimHandlerDto, isAlreadySentChangeRequest, user);

                    if (AppConstant.DESKTOP_OFFER_TYPE != lossType && !searchedClaimHandlerDto.getLossType().equals(lossType)) {
                        updateDefineDocument(connection, motorEngineerDetailsDto, lossType, user);
                    }
                    break;

                case AppConstant.DR_INSPECTION://DR Insepection = 5
                case AppConstant.SUP_INSPECTION://Supplimantary Inspection = 6
                    updateTotalApprovedAcrByDrSupplementaryInspection(connection, motorEngineerDetailsDto, searchedClaimHandlerDto, isAlreadySentChangeRequest, user);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateTotalApprovedAcrByDrSupplementaryInspection(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, ClaimHandlerDto searchedClaimHandlerDto, boolean isAlreadySentChangeRequest, UserDto user) throws Exception {
        BigDecimal changeAcrAmount;
        BigDecimal acrAmount;
        BigDecimal previousAcr = BigDecimal.ZERO;
        try {
            DrSupplementaryInspectionDetailsDto prevDrSupplementaryInspectionDetailsDto = drSupplementaryInspectionDetailsMeDao.searchMaster(connection, motorEngineerDetailsDto.getRefNo());
            if (null != prevDrSupplementaryInspectionDetailsDto && (motorEngineerDetailsDto.getInspectionDetailsDto().getRecordStatus() == ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus() || isAlreadySentChangeRequest)) {
                if (motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAcr().compareTo(prevDrSupplementaryInspectionDetailsDto.getOldAcr()) > 0) {
                    changeAcrAmount = motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAcr().subtract(prevDrSupplementaryInspectionDetailsDto.getOldAcr());
                } else {
                    changeAcrAmount = getChangeAcrAmountIfAlreadyApprovedPayment(connection, motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAcr(), prevDrSupplementaryInspectionDetailsDto.getOldAcr(), searchedClaimHandlerDto.getReserveAmount(), searchedClaimHandlerDto.getClaimNo());
                }
                acrAmount = searchedClaimHandlerDto.getAprvTotAcrAmount().add(changeAcrAmount);
                previousAcr = prevDrSupplementaryInspectionDetailsDto.getOldAcr();
            } else {
                changeAcrAmount = motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAcr();
                acrAmount = searchedClaimHandlerDto.getAprvTotAcrAmount().add(motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAcr());
            }

            sendLargeClaimEmail(connection, searchedClaimHandlerDto.getClaimNo(), acrAmount, previousAcr, isAlreadySentChangeRequest, user);
            updateClaimReserve(connection, motorEngineerDetailsDto, user, changeAcrAmount, acrAmount, searchedClaimHandlerDto.getReserveAmount(), searchedClaimHandlerDto.getReserveAmountAfterAprv(), "DR/Sup. Inspection");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }

    }

    private void updateTotalApprovedAcrByDesktopInspection(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, ClaimHandlerDto searchedClaimHandlerDto, boolean isAlreadySentChangeRequest, UserDto user) throws Exception {
        BigDecimal changeAcrAmount;
        BigDecimal acrAmount;
        BigDecimal reserveAmount = searchedClaimHandlerDto.getReserveAmount();
        BigDecimal reserveAmountAfterApproved = searchedClaimHandlerDto.getReserveAmountAfterAprv();
        BigDecimal previousAcr = BigDecimal.ZERO;
        try {
            if ((motorEngineerDetailsDto.getInspectionDetailsDto().getRecordStatus() == ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus() || isAlreadySentChangeRequest)) {
                DesktopInspectionDetailsDto prevDesktopInspectionDetailsDto = desktopInspectionDetailsDao.searchMaster(connection, motorEngineerDetailsDto.getRefNo());
                if (motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAcr().compareTo(prevDesktopInspectionDetailsDto.getOldAcr()) > 0) {
                    changeAcrAmount = motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAcr().subtract(prevDesktopInspectionDetailsDto.getOldAcr());
                } else {
                    changeAcrAmount = getChangeAcrAmountIfAlreadyApprovedPayment(connection, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAcr(), prevDesktopInspectionDetailsDto.getOldAcr(), reserveAmount, searchedClaimHandlerDto.getClaimNo());
                }
                acrAmount = searchedClaimHandlerDto.getAprvTotAcrAmount().add(changeAcrAmount);
                previousAcr = prevDesktopInspectionDetailsDto.getOldAcr();
            } else {
                acrAmount = getNewAcrAmountIfAlreadyApprovedPayment(connection, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAcr(), reserveAmount, searchedClaimHandlerDto.getClaimNo());
                changeAcrAmount = acrAmount;
                reserveAmount = BigDecimal.ZERO;
                reserveAmountAfterApproved = BigDecimal.ZERO;
            }

            if (motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAdvancedAmount().compareTo(BigDecimal.ZERO) > 0) {
                updateDesktopAdvanceAmount(connection, motorEngineerDetailsDto, changeAcrAmount, reserveAmountAfterApproved, user);
            }

            if (null != motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getIsOnsitePending() && AppConstant.YES.equalsIgnoreCase(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getIsOnsitePending())) {
                saveClaimsLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, "Pending on-site / off-site inspection Alert", "\"Marked as OK to \"This claim has a pending on-site / off-site inspection. Do you want to proceed?\" alert");
            }

            sendLargeClaimEmail(connection, searchedClaimHandlerDto.getClaimNo(), acrAmount, previousAcr, isAlreadySentChangeRequest, user);
            updateClaimReserve(connection, motorEngineerDetailsDto, user, changeAcrAmount, acrAmount, reserveAmount, reserveAmountAfterApproved, "Desktop Inspection");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }

    }

    private void updateTotalApprovedAcrByGarageInspection(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, ClaimHandlerDto searchedClaimHandlerDto, boolean isAlreadySentChangeRequest, UserDto user) throws Exception {
        BigDecimal changeAcrAmount;
        BigDecimal acrAmount;
        BigDecimal reserveAmount = searchedClaimHandlerDto.getReserveAmount();
        BigDecimal reserveAmountAfterApproved = searchedClaimHandlerDto.getReserveAmountAfterAprv();
        BigDecimal previousAcr = BigDecimal.ZERO;
        try {
            if ((motorEngineerDetailsDto.getInspectionDetailsDto().getRecordStatus() == ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus() || isAlreadySentChangeRequest)) {
                GarageInspectionDetailsDto prevGarageInspectionDetailsDto = garageInspectionDetailsMeDao.searchMaster(connection, motorEngineerDetailsDto.getRefNo());
                if (motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr().compareTo(prevGarageInspectionDetailsDto.getOldAcr()) > 0) {
                    changeAcrAmount = motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr().subtract(prevGarageInspectionDetailsDto.getOldAcr());
                } else {
                    changeAcrAmount = getChangeAcrAmountIfAlreadyApprovedPayment(connection, motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr(), prevGarageInspectionDetailsDto.getOldAcr(), reserveAmount, searchedClaimHandlerDto.getClaimNo());
                }
                acrAmount = searchedClaimHandlerDto.getAprvTotAcrAmount().add(changeAcrAmount);
                previousAcr = prevGarageInspectionDetailsDto.getOldAcr();
            } else {
                acrAmount = getNewAcrAmountIfAlreadyApprovedPayment(connection, motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr(), reserveAmount, searchedClaimHandlerDto.getClaimNo());
                changeAcrAmount = acrAmount;
                reserveAmount = BigDecimal.ZERO;
                reserveAmountAfterApproved = BigDecimal.ZERO;
            }
            if (motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAdvancedAmount().compareTo(BigDecimal.ZERO) > 0) {
                updateGarageAdvanceAmount(connection, motorEngineerDetailsDto, changeAcrAmount, reserveAmountAfterApproved, user);
            }
            if (null != motorEngineerDetailsDto.getGarageInspectionDetailsDto().getIsOnsitePending() && AppConstant.YES.equalsIgnoreCase(motorEngineerDetailsDto.getGarageInspectionDetailsDto().getIsOnsitePending())) {
                saveClaimsLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, "Pending on-site / off-site inspection Alert", "\"Marked as OK to \"This claim has a pending on-site / off-site inspection. Do you want to proceed?\" alert");
            }
            sendLargeClaimEmail(connection, searchedClaimHandlerDto.getClaimNo(), acrAmount, previousAcr, isAlreadySentChangeRequest, user);
            updateClaimReserve(connection, motorEngineerDetailsDto, user, changeAcrAmount, acrAmount, reserveAmount, reserveAmountAfterApproved, "Garage Inspection");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateTotalApprovedAcrByOnsiteInspection(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, ClaimHandlerDto searchedClaimHandlerDto, boolean isAlreadySentChangeRequest, UserDto user) throws Exception {
        BigDecimal changeAcrAmount;
        BigDecimal acrAmount;
        BigDecimal reserveAmount = searchedClaimHandlerDto.getReserveAmount();
        BigDecimal reserveAmountAfterApproved = searchedClaimHandlerDto.getReserveAmountAfterAprv();
        BigDecimal previousAcr = BigDecimal.ZERO;
        try {
            if ((motorEngineerDetailsDto.getInspectionDetailsDto().getRecordStatus() == ClaimStatus.CLAIM_CHANGE_REQUESTED.getClaimStatus() || isAlreadySentChangeRequest)) {
                OnSiteInspectionDetailsDto prevOnSiteInspectionDetailsDto = onSiteInspectionDetailsMeDao.searchMaster(connection, motorEngineerDetailsDto.getRefNo());
                if (motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr().compareTo(prevOnSiteInspectionDetailsDto.getOldAcr()) > 0) {
                    changeAcrAmount = motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr().subtract(prevOnSiteInspectionDetailsDto.getOldAcr());
                } else {
                    changeAcrAmount = getChangeAcrAmountIfAlreadyApprovedPayment(connection, motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr(), prevOnSiteInspectionDetailsDto.getOldAcr(), reserveAmount, searchedClaimHandlerDto.getClaimNo());
                }
                acrAmount = searchedClaimHandlerDto.getAprvTotAcrAmount().add(changeAcrAmount);
                previousAcr = prevOnSiteInspectionDetailsDto.getOldAcr();
            } else {
                acrAmount = getNewAcrAmountIfAlreadyApprovedPayment(connection, motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr(), reserveAmount, searchedClaimHandlerDto.getClaimNo());
                changeAcrAmount = acrAmount;
                reserveAmount = BigDecimal.ZERO;
                reserveAmountAfterApproved = BigDecimal.ZERO;
            }
            sendLargeClaimEmail(connection, searchedClaimHandlerDto.getClaimNo(), acrAmount, previousAcr, isAlreadySentChangeRequest, user);
            updateClaimReserve(connection, motorEngineerDetailsDto, user, changeAcrAmount, acrAmount, reserveAmount, reserveAmountAfterApproved, "On-site/Off-site Inspection ");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private BigDecimal getChangeAcrAmountIfAlreadyApprovedPayment(Connection connection, BigDecimal newAcr, BigDecimal previousAcr, BigDecimal reserveAmount, Integer claimNo) throws Exception {
        BigDecimal changeAcrAmount;
        try {
            BigDecimal totalApprovedPaymentAmount = claimCalculationSheetMainDao.getTotalApprovedPaymentAmount(connection, claimNo);
            if (totalApprovedPaymentAmount.compareTo(reserveAmount.subtract(previousAcr.subtract(newAcr))) > 0) {
                throw new WrongValueException(AppConstant.ERROR_MESSAGE, "Record can not submit. Reserve amount can not be less than total paid amount");
            } else {
                changeAcrAmount = newAcr.subtract(previousAcr);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return changeAcrAmount;
    }

    private BigDecimal getNewAcrAmountIfAlreadyApprovedPayment(Connection connection, BigDecimal newAcr, BigDecimal reserveAmount, Integer claimNo) throws Exception {
        BigDecimal newAcrAmount;
        try {
            BigDecimal totalApprovedPaymentAmount = claimCalculationSheetMainDao.getTotalApprovedPaymentAmount(connection, claimNo);
            if (reserveAmount.compareTo(newAcr) > 0 && totalApprovedPaymentAmount.compareTo(newAcr) > 0) {
                throw new WrongValueException(AppConstant.ERROR_MESSAGE, "Record can not submit. Reserve amount can not be less than total paid amount");
            } else {
                newAcrAmount = newAcr;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return newAcrAmount;
    }

    private void updateClaimReserve(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user, BigDecimal changeAcrAmount, BigDecimal acrAmount, BigDecimal reserveAmount, BigDecimal reserveAmountAfterApproved, String inspectionType) throws Exception {
        try {
            BigDecimal approveAdvancedAmount = claimHandlerDao.getAdvanceAmount(connection, motorEngineerDetailsDto.getClaimNo());
            if (approveAdvancedAmount.compareTo(changeAcrAmount.add(reserveAmountAfterApproved)) > 0) {
                throw new WrongValueException(AppConstant.ERROR_MESSAGE, "Record can not submit. current provision can not be less than balance advance amount");
            }
            if (null != acrAmount && acrAmount.compareTo(BigDecimal.ZERO) > 0) {
                claimHandlerDao.updateReserveAmountFromRTE(changeAcrAmount.add(reserveAmount), changeAcrAmount.add(reserveAmountAfterApproved), acrAmount, motorEngineerDetailsDto.getInspectionDetailsAuthUserId(), connection, motorEngineerDetailsDto.getClaimNo());

                updateCalsheetTempData(connection, motorEngineerDetailsDto, changeAcrAmount, acrAmount);

                offlineReserveClaim(connection, motorEngineerDetailsDto, changeAcrAmount.add(reserveAmount), 1, user, inspectionType);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateCalsheetTempData(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, BigDecimal changeAcrAmount, BigDecimal acrAmount) throws Exception {
        int inspectionTypeId = motorEngineerDetailsDto.getInspectionDto().getInspectionId();
        try {
            switch (inspectionTypeId) {
                case AppConstant.GARAGE_INSPECTION:
                    updateCalsheetTempDetailsWithAdvanceAmount(connection, changeAcrAmount, acrAmount, motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAdvancedAmount(), motorEngineerDetailsDto.getClaimNo());
                    break;
                case AppConstant.DESKTOP_INSPECTION:
                    updateCalsheetTempDetailsWithAdvanceAmount(connection, changeAcrAmount, acrAmount, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAdvancedAmount(), motorEngineerDetailsDto.getClaimNo());
                    break;
                default:
                    updateCalsheetTempDetails(connection, changeAcrAmount, acrAmount, motorEngineerDetailsDto.getClaimNo());
                    break;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updatePenaltyAmount(Connection connection, BigDecimal boldPercent, BigDecimal boldTyrePenaltyAmount, BigDecimal underPenaltyPercent, BigDecimal underPenaltyAmount, ClaimHandlerDto searchedClaimHandlerDto) throws Exception {
        try {

            BigDecimal penaltyAmount = boldTyrePenaltyAmount.add(null == searchedClaimHandlerDto.getPenaltyBaldTyre() ? BigDecimal.ZERO : searchedClaimHandlerDto.getPenaltyBaldTyre());
            claimHandlerDao.updatePenaltyAmount(connection, searchedClaimHandlerDto.getClaimNo(), penaltyAmount, boldPercent, true);

            penaltyAmount = underPenaltyAmount.add(null == searchedClaimHandlerDto.getPenaltyUnderInsurce() ? BigDecimal.ZERO : searchedClaimHandlerDto.getPenaltyUnderInsurce());
            claimHandlerDao.updatePenaltyAmount(connection, searchedClaimHandlerDto.getClaimNo(), penaltyAmount, underPenaltyPercent, false);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateDesktopAdvanceAmount(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, BigDecimal changeAcrAmount, BigDecimal reserveAmountAfterAprv, UserDto user) throws Exception {
        try {
            BigDecimal newAdvancedAmount;
            BigDecimal approveAdvancedAmount = claimHandlerDao.getAdvanceAmount(connection, motorEngineerDetailsDto.getClaimNo());
            boolean isAdvanceAmountAdded = AppConstant.ADD.equalsIgnoreCase(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAdvanceChange());
            if (isAdvanceAmountAdded) {
                newAdvancedAmount = approveAdvancedAmount.add(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAdvancedAmount());
            } else {
                newAdvancedAmount = approveAdvancedAmount.subtract(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAdvancedAmount());
            }

//            if (isAvailableCalculationSheet) {
//                BigDecimal paidAdvanceAmount = claimCalculationSheetMainDao.getPaidTotalAdvanceAmount(connection, motorEngineerDetailsDto.getClaimNo());
//                newAdvancedAmount = newAdvancedAmount.subtract(paidAdvanceAmount);
//            }

            if (BigDecimal.ZERO.compareTo(newAdvancedAmount) > 0) {
                setAdvanceAmountDetails(connection, motorEngineerDetailsDto, motorEngineerDetailsDto.getClaimNo());
                motorEngineerDetailsDto.getInspectionDetailsDto().setInspectionDetailsAuthStatus("N");
                throw new WrongValueException(AppConstant.APPROVE_ADVANCE_AMOUNT, "Balance Advance Amount Cannot be less than zero");
            }

            if (newAdvancedAmount.compareTo(changeAcrAmount.add(reserveAmountAfterAprv)) > 0) {
                setAdvanceAmountDetails(connection, motorEngineerDetailsDto, motorEngineerDetailsDto.getClaimNo());
                motorEngineerDetailsDto.getInspectionDetailsDto().setInspectionDetailsAuthStatus("N");
                throw new WrongValueException(AppConstant.ADVANCE_AMOUNT, "Advance Amount - Rs." + newAdvancedAmount + " Cannot be Greater than Current Provision - Rs." + changeAcrAmount.add(reserveAmountAfterAprv));
            }

            claimHandlerDao.updateAdvanceAmount(connection, motorEngineerDetailsDto.getClaimNo(), newAdvancedAmount);

            if (isAdvanceAmountAdded) {
                saveClaimsLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, "Advance Amount Added", "Advance Amount Add By [Desktop Inspection] RTE, Added Amount Rs." + motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAdvancedAmount() + ", Updated Approve Advance Amount Rs." + newAdvancedAmount);
            } else {
                saveClaimsLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, "Advance Amount Reduced", "Advance Amount Reduce By [Desktop Inspection] RTE, Reduced Amount Rs." + motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAdvancedAmount() + ", Updated Approve Advance Amount Rs." + newAdvancedAmount);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    private void updateGarageAdvanceAmount(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, BigDecimal changeAcrAmount, BigDecimal reserveAmountAfterAprv, UserDto user) throws Exception {

        try {
            BigDecimal newAdvancedAmount;
            BigDecimal approveAdvancedAmount = claimHandlerDao.getAdvanceAmount(connection, motorEngineerDetailsDto.getClaimNo());
            boolean isAdvanceAmountAdded = AppConstant.ADD.equalsIgnoreCase(motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAdvanceChange());
            if (isAdvanceAmountAdded) {
                newAdvancedAmount = approveAdvancedAmount.add(motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAdvancedAmount());
            } else {
                newAdvancedAmount = approveAdvancedAmount.subtract(motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAdvancedAmount());
            }

//            if (isAvailableCalculationSheet) {
//                BigDecimal paidAdvanceAmount = claimCalculationSheetMainDao.getPaidTotalAdvanceAmount(connection, motorEngineerDetailsDto.getClaimNo());
//                newAdvancedAmount = newAdvancedAmount.subtract(paidAdvanceAmount);
//            }

            if (BigDecimal.ZERO.compareTo(newAdvancedAmount) > 0) {
                setAdvanceAmountDetails(connection, motorEngineerDetailsDto, motorEngineerDetailsDto.getClaimNo());
                motorEngineerDetailsDto.getInspectionDetailsDto().setInspectionDetailsAuthStatus("N");
                throw new WrongValueException(AppConstant.APPROVE_ADVANCE_AMOUNT, "Balance Advance Amount Cannot be less than zero");
            }

            if (newAdvancedAmount.compareTo(changeAcrAmount.add(reserveAmountAfterAprv)) > 0) {
                setAdvanceAmountDetails(connection, motorEngineerDetailsDto, motorEngineerDetailsDto.getClaimNo());
                motorEngineerDetailsDto.getInspectionDetailsDto().setInspectionDetailsAuthStatus("N");
                throw new WrongValueException(AppConstant.ADVANCE_AMOUNT, "Balance Advance Amount - Rs." + newAdvancedAmount + " Cannot be Greater than Current Provision - Rs." + changeAcrAmount.add(reserveAmountAfterAprv));
            }

            claimHandlerDao.updateAdvanceAmount(connection, motorEngineerDetailsDto.getClaimNo(), newAdvancedAmount);

            if (isAdvanceAmountAdded) {
                saveClaimsLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, "Advance Amount Added", "Advance Amount Add By [Garage Inspection] RTE, Added Amount Rs." + motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAdvancedAmount() + ", Updated Approve Advance Amount Rs." + newAdvancedAmount);
            } else {
                saveClaimsLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, "Advance Amount Reduced", "Advance Amount Reduce By [Garage Inspection] RTE, Reduced Amount Rs." + motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAdvancedAmount() + ", Updated Approve Advance Amount Rs." + newAdvancedAmount);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }

    }

    private void updateCalsheetTempDetailsWithAdvanceAmount(Connection connection, BigDecimal changeAcrAmount, BigDecimal acrAmount, BigDecimal advancedAmount, int claimNo) throws Exception {
        try {
            List<ClaimCalculationSheetMainTempDto> tempList = claimCalculationSheetMainTempDao.searchAllByClaimNo(connection, claimNo);
            for (ClaimCalculationSheetMainTempDto dto : tempList) {
                dto.setTotalApprovedAcr(acrAmount);
                dto.setReserveAmount(acrAmount);
                dto.setPrevReserveAmount(acrAmount);
                dto.setReserveAmountAfterApproved(acrAmount);
                dto.setPrevReserveAmountAfterApproved(acrAmount);
                dto.setAdvanceAmount(advancedAmount);
                dto.setPrevAdvanceAmount(advancedAmount);
                claimCalculationSheetMainTempDao.updateTempDetailsWhenAcrIsUpdated(connection, dto);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    private void updateCalsheetTempDetails(Connection connection, BigDecimal changeAcrAmount, BigDecimal acrAmount, Integer claimNo) throws Exception {

        try {
            List<ClaimCalculationSheetMainTempDto> tempList = claimCalculationSheetMainTempDao.searchAllByClaimNo(connection, claimNo);
            for (ClaimCalculationSheetMainTempDto dto : tempList) {
                dto.setTotalApprovedAcr(acrAmount);
                dto.setReserveAmount(changeAcrAmount.add(dto.getReserveAmount()));
                dto.setPrevReserveAmount(changeAcrAmount.add(dto.getPrevReserveAmount()));
                dto.setReserveAmountAfterApproved(changeAcrAmount.add(dto.getReserveAmountAfterApproved()));
                dto.setPrevReserveAmountAfterApproved(changeAcrAmount.add(dto.getPrevReserveAmountAfterApproved()));
                claimCalculationSheetMainTempDao.updateTempDetailsWhenAcrIsUpdated(connection, dto);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }

    }

    private BigDecimal getChangeAcrValue(BigDecimal aprvTotAcrAmount, BigDecimal acrAmount, BigDecimal prevAcr) {
        return aprvTotAcrAmount.subtract(prevAcr.subtract(acrAmount));
    }

    private void saveSpecialRemarkToChangeRequest(UserDto user, String remark, Integer claimNo, Connection connection, boolean isRteChange) throws Exception {
        SpecialRemarkDto specialRemarkDto = new SpecialRemarkDto();
        try {
            specialRemarkDto.setInputDateTime(Utility.sysDateTime());
            specialRemarkDto.setInputUserId(user.getUserId());
            specialRemarkDto.setClaimNo(claimNo);
            specialRemarkDto.setRemark(remark);
            if (isRteChange) {
                specialRemarkDto.setDepartmentId(AppConstant.MOTOR_ENGINEER_MODULE);
                specialRemarkDto.setSectionName(AppConstant.MOTOR_ENGINEER_MODULE_SECTION);
            } else {
                specialRemarkDto.setDepartmentId(AppConstant.ASSESSOR_DEPARTMENT_ID);
                specialRemarkDto.setSectionName(AppConstant.ASSESSOR_MODULE_SECTION);
            }

            specialRemarkDao.insertMaster(connection, specialRemarkDto);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }
    }

    private void saveNotification(UserDto user, InspectionDetailsDto inspectionDetailsDto, Connection connection, boolean isRteChange) throws Exception {
        try {
            String message = "Change Request opened to ".concat(inspectionDetailsDto.getAssessorAllocationDto().getJobId());
            String url = "";
            String assignUserId = "";
            if (isRteChange) {
                url = AppConstant.MOTORENG_VIEW.concat("?P_N_REF_NO=").concat(Integer.toString(inspectionDetailsDto.getRefNo()));
                assignUserId = inspectionDetailsDto.getAssignRteUser();
            } else {
                if (null != inspectionDetailsDto.getAssessorAllocationDto().getAssessorDto()) {
                    AssessorDto assessorUserName = assessorDao.getAssessorUserName(connection, inspectionDetailsDto.getAssessorAllocationDto().getAssessorDto().getCode());
                    url = AppConstant.ASSESSOR_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(inspectionDetailsDto.getClaimNo())).concat("&P_N_REF_NO=").concat(Integer.toString(inspectionDetailsDto.getRefNo()));
                    assignUserId = assessorUserName.getUserName();
                }
            }
            saveCommonNotification(connection, inspectionDetailsDto.getClaimNo(), user.getUserId(), assignUserId, message, url, AppConstant.DEFAULT_NOTIFICATION_COLOR_CODE, inspectionDetailsDto.getRefNo(), NotificationPriority.LOW.getNotificationPriority());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }
    }

    private BigDecimal getAcrValue(MotorEngineerDetailsDto motorEngineerDetailsDto) {
        BigDecimal acr;
        switch (motorEngineerDetailsDto.getInspectionDto().getInspectionId()) {
            case 1:
            case 2:
            case 3:
            case 9:
            case 10:
            case 11:
                acr = null == motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr() ? BigDecimal.ZERO : motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().getAcr();
                break;
            case 4:
                acr = null == motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr() ? BigDecimal.ZERO : motorEngineerDetailsDto.getGarageInspectionDetailsDto().getAcr();
                break;
            case 5:
            case 6:
                acr = null == motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAcr() ? BigDecimal.ZERO : motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().getAcr();
                break;
            case 8:
                acr = null == motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAcr() ? BigDecimal.ZERO : motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getAcr();
                break;
            default:
                acr = BigDecimal.ZERO;
                break;

        }
        return acr;
    }

    private void inspectionDetailsToDefault(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) throws Exception {

        motorEngineerDetailsDto.setInspectionDetailsAuthStatus("P");
        motorEngineerDetailsDto.setInspectionDetailsAuthUserId(user.getUserId());
        motorEngineerDetailsDto.setInspectionDetailsAuthDatetime(AppConstant.DEFAULT_DATE_TIME);
        try {
            inspectionDetailsDao.updateInspectionDetailAuthByRefNo(connection, motorEngineerDetailsDto.getInspectionDetailsAuthStatus(), motorEngineerDetailsDto.getInspectionDetailsAuthUserId(), motorEngineerDetailsDto.getInspectionDetailsAuthDatetime(), motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());

            switch (motorEngineerDetailsDto.getInspectionDto().getInspectionId()) {
                case 4: //Garage Inspection
                    motorEngineerDetailsDto.getGarageInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());

                    garageInspectionDetailsMeDao.updateMaster(connection, motorEngineerDetailsDto.getGarageInspectionDetailsDto());
                    break;
                case 5: //DR Insepection
                case 6: //Supplimantary Inspection
                    motorEngineerDetailsDto.getDrSuppInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
                    drSupplementaryInspectionDetailsMeDao.updateMaster(connection, motorEngineerDetailsDto.getDrSuppInspectionDetailsDto());
                    break;
                case 7: //After Repair inspection
                case 9:   //Salvage Inspection
                    motorEngineerDetailsDto.getAriInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
                    aRIInspectionDetailsMeDao.updateMaster(connection, motorEngineerDetailsDto.getAriInspectionDetailsDto());
                    break;
                case 8: //Desktop Assesment
                    motorEngineerDetailsDto.getDesktopInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
                    desktopInspectionDetailsMeDao.updateMaster(connection, motorEngineerDetailsDto.getDesktopInspectionDetailsDto());
                    break;
                default://On site Inspection
                    motorEngineerDetailsDto.getOnSiteInspectionDetailsDto().setRefNo(motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());
                    onSiteInspectionDetailsMeDao.updateMaster(connection, motorEngineerDetailsDto.getOnSiteInspectionDetailsDto());
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }
    }

    private void initialLog(InspectionDetailsDto inspectionDetailsDto, Connection connection, UserDto user, String fieldName, String fieldValue) throws Exception {
        List<ClaimLogTrailDto> loggerTrailList = new ArrayList<>();
        ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
        claimLogTrailDto.setFieldName(fieldName);
        claimLogTrailDto.setFieldValue(fieldValue);
        claimLogTrailDto.setClaimNo(inspectionDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
        claimLogTrailDto.setInputDateTime(Utility.sysDateTime());
        claimLogTrailDto.setUserId(user.getUserId());
        claimLogTrailDto.setFormNameId(AppConstant.MOTOR_ENGINEER_MODULE_LOG);
        loggerTrailList.add(claimLogTrailDto);
        loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, inspectionDetailsDto.getClaimNo(), inspectionDetailsDto.getRefNo(), inspectionDetailsDto.getInputUserId(), 1);

    }

    private AssessorPaymentDetailsDto getAssessorPaymentDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) throws Exception {
        AssessorPaymentDetailsDto assessorPaymentDetailsDto = new AssessorPaymentDetailsDto();
        try {
            assessorPaymentDetailsDto.setClaimNo(motorEngineerDetailsDto.getClaimNo());
            assessorPaymentDetailsDto.setKeyId(motorEngineerDetailsDto.getRefNo());
            assessorPaymentDetailsDto.setType(AppConstant.ASSESSOR_PAYMENT);
            assessorPaymentDetailsDto.setMilleage(motorEngineerDetailsDto.getMileage());
            assessorPaymentDetailsDto.setCostOfCall(motorEngineerDetailsDto.getCostOfCall());
            assessorPaymentDetailsDto.setOtherFee(motorEngineerDetailsDto.getOtherFee());
            assessorPaymentDetailsDto.setDeductionFee(motorEngineerDetailsDto.getDeductions());
            assessorPaymentDetailsDto.setTotalFee(motorEngineerDetailsDto.getTotalAssessorFee());
            calculateProfessionalFee(motorEngineerDetailsDto, assessorPaymentDetailsDto);
            assessorPaymentDetailsDto.setPaymentStatus(PaymentStatus.Pending);
            assessorPaymentDetailsDto.setRecordStatus(1);
            assessorPaymentDetailsDto.setInpUserId(user.getUserId());
            assessorPaymentDetailsDto.setInpDate(Utility.sysDateTime());
            assessorPaymentDetailsDto.setName(motorEngineerDetailsDto.getAssessorAllocationDto().getAssessorDto().getName());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception(e);
        }
        return assessorPaymentDetailsDto;
    }

    private void calculateProfessionalFee(MotorEngineerDetailsDto motorEngineerDetailsDto, AssessorPaymentDetailsDto assessorPaymentDetailsDto) {

        try {
            ClaimInspectionTypeDto claimInspectionTypeDto = inspectionDetailsService.getInspectionTypeDto(Integer.toString(motorEngineerDetailsDto.getInspectionDto().getInspectionId()));
            BigDecimal travelFee = BigDecimal.ZERO;
            BigDecimal professionalFee = BigDecimal.ZERO;
            String assessorType = inspectionDetailsService.getAssessorTypeByRefNo(String.valueOf(motorEngineerDetailsDto.getRefNo()));

            BigDecimal mileageFee;

            if ("1".equals(motorEngineerDetailsDto.getJobType().toString())) { //DAY
                if (AppConstant.INHOUSE.equalsIgnoreCase(assessorType)) {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.INHOUSE);
                    travelFee = new BigDecimal(motorEngineerDetailsDto.getMileage()).multiply(mileageFee);
                    professionalFee = claimInspectionTypeDto.getInhouseDayDutyAmt();
                } else if (AppConstant.FREELANCE.equalsIgnoreCase(assessorType)) {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.FREELANCE);
                    travelFee = new BigDecimal(motorEngineerDetailsDto.getMileage()).multiply(mileageFee);
                    professionalFee = claimInspectionTypeDto.getFreelanceDayDutyAmt();
                } else {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.PERMANENT);
                    travelFee = new BigDecimal(motorEngineerDetailsDto.getMileage()).multiply(mileageFee);
                    professionalFee = claimInspectionTypeDto.getPermanantDayDutyAmt();
                }
            } else if ("2".equals(motorEngineerDetailsDto.getJobType().toString())) { //NIGHT
                if (AppConstant.INHOUSE.equalsIgnoreCase(assessorType)) {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.INHOUSE);
                    travelFee = new BigDecimal(motorEngineerDetailsDto.getMileage()).multiply(mileageFee);
                    professionalFee = claimInspectionTypeDto.getInhouseNightDutyAmt();
                } else if (AppConstant.FREELANCE.equalsIgnoreCase(assessorType)) {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.FREELANCE);
                    travelFee = new BigDecimal(motorEngineerDetailsDto.getMileage()).multiply(mileageFee);
                    professionalFee = claimInspectionTypeDto.getFreelanceNightDutyAmt();
                } else {
                    mileageFee = inspectionDetailsService.getMileageFee(AppConstant.PERMANENT);
                    travelFee = new BigDecimal(motorEngineerDetailsDto.getMileage()).multiply(mileageFee);
                    professionalFee = claimInspectionTypeDto.getPermanantNightDutyAmt();
                }
            }
            assessorPaymentDetailsDto.setTravelFee(travelFee);
            assessorPaymentDetailsDto.setProfessionalFee(professionalFee);

        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
        }

    }

    private void saveAri(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) {
        try {
            RequestAriDto requestAri = requestAriDao.searchByClaimNo(connection, motorEngineerDetailsDto.getClaimNo());
            RequestAriDto requestAriDto = new RequestAriDto();
            if (null == requestAri) {
                requestAriDto.setClaimNo(motorEngineerDetailsDto.getClaimNo());
                ClaimsDto polClaim = callCenterService.getReportAccidentClaimsDtoByClaimNo(motorEngineerDetailsDto.getClaimNo());
                requestAriDto.setAssigningAssessorCode(AppConstant.STRING_EMPTY);
                requestAriDto.setRteCode(user.getUserId());
                requestAriDto.setCustomerName(polClaim.getPolicyDto().getCustName());
                requestAriDto.setAddress1(polClaim.getPolicyDto().getCustAddressLine1());
                requestAriDto.setAddress2(polClaim.getPolicyDto().getCustAddressLine2());
                requestAriDto.setAddress3(polClaim.getPolicyDto().getCustAddressLine3());
                requestAriDto.setContactNo(polClaim.getPolicyDto().getCustMobileNo());
                requestAriDto.setRefNo(motorEngineerDetailsDto.getAssessorAllocationDto().getJobId());
                requestAriService.insert(requestAriDto, user, connection);
                InspectionDetailsDto inspectionDetailsDto = new InspectionDetailsDto();
                inspectionDetailsDto.setClaimNo(motorEngineerDetailsDto.getClaimNo());
                initialLog(motorEngineerDetailsDto.getClaimNo(), motorEngineerDetailsDto.getRefNo(), connection, user, "ARI", "ARI Requested");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void initialLog(Integer claimNo, Integer refNo, Connection connection, UserDto user, String fieldName, String fieldValue) throws Exception {
        List<ClaimLogTrailDto> loggerTrailList = new ArrayList<>();
        ClaimLogTrailDto claimLogTrailDto = new ClaimLogTrailDto();
        claimLogTrailDto.setFieldName(fieldName);
        claimLogTrailDto.setFieldValue(fieldValue);
        claimLogTrailDto.setClaimNo(claimNo);
        claimLogTrailDto.setInputDateTime(Utility.sysDateTime());
        claimLogTrailDto.setUserId(user.getUserId());
        claimLogTrailDto.setFormNameId(AppConstant.MOTOR_ENGINEER_MODULE);
        loggerTrailList.add(claimLogTrailDto);
        loggerTrailDao.insertLoggerTrailList(connection, loggerTrailList, claimNo, refNo, user.getUserId(), 1);

    }

    private void offlineAssessorFee(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {


        try {
            ClaimsDto claimsDto = callCenterDao.searchMaster(connection, motorEngineerDetailsDto.getClaimNo());
            AssessorAllocationDto assessorAllocationDto = assessorAllocationDao.searchMaster(connection, motorEngineerDetailsDto.getRefNo());
            McmsClaimOfflineReserveAssessorDto offlineReserveAssessorDto = new McmsClaimOfflineReserveAssessorDto();
//            PolicyDto policyDto = policyDao.searchMaster(connection, claimsDto.getPolRefNo());
            if (null != claimsDto && null != assessorAllocationDto) {

                offlineReserveAssessorDto = setOtherAmountAndScheduleAmount(connection, offlineReserveAssessorDto, motorEngineerDetailsDto, assessorAllocationDto.getAssessorDto().getCode());

                offlineReserveAssessorDto.setOvClaimNo(claimsDto.getIsfClaimNo());
                offlineReserveAssessorDto.setOvIdentificationNo(assessorAllocationDto.getAssessorDto().getCode());
                offlineReserveAssessorDto.setOvPanelCategory(AppConstant.PANEL_CATEGORY);
                offlineReserveAssessorDto.setOvInstitutionBranch(AppConstant.STRING_EMPTY);
                offlineReserveAssessorDto.setOvInstitutionCode(AppConstant.STRING_EMPTY);
                offlineReserveAssessorDto.setOvIdentificationCode(AppConstant.IDENTIFICATION_CODE);
                offlineReserveAssessorDto.setDInsertDateTime(Utility.sysDateTime());
                offlineReserveAssessorDto.setNRetryAttempt(AppConstant.ZERO_INT);
                offlineReserveAssessorDto.setDIsfsUpdateDateTime(AppConstant.DEFAULT_DATE_TIME);
                offlineReserveAssessorDto.setVIsfsUpdateStat(AppConstant.NO);
                offlineReserveAssessorDto.setClaimNo(motorEngineerDetailsDto.getClaimNo());
                offlineReserveAssessorDto.setPolicyChannelType(claimsDto.getPolicyChannelType());
                offlineReserveAssessorDao.insertMaster(connection, offlineReserveAssessorDto);

            }

        } catch (WrongValueException e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception(e);
        }

    }

    private McmsClaimOfflineReserveAssessorDto setOtherAmountAndScheduleAmount(Connection connection, McmsClaimOfflineReserveAssessorDto offlineReserveAssessorDto, MotorEngineerDetailsDto motorEngineerDetailsDto, String code) throws Exception {
        BigDecimal otherAmount;
        BigDecimal scheduleAmount;
        AssessorPaymentDeductionDetailDto assessorPaymentDeductionDetailDto = new AssessorPaymentDeductionDetailDto();
        try {
            ApproveAssessorPaymentClaimWiseDto approveAssessorPaymentClaimWiseDto = approveAssessorPaymentClaimWiseDao.getDetailByAssessorCodeAndClaimNo(connection, motorEngineerDetailsDto.getClaimNo(), code);
            otherAmount = getOtherAmount(motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getOtherFee(), motorEngineerDetailsDto.getMileage(), motorEngineerDetailsDto.getCostOfCall());
            scheduleAmount = getScheduleAmount(motorEngineerDetailsDto.getInspectionDto().getInspectionId(), motorEngineerDetailsDto.getRefNo(), motorEngineerDetailsDto.getJobType());
            assessorPaymentDeductionDetailDto.setBeforeDeductionOtherAmount(otherAmount);
            assessorPaymentDeductionDetailDto.setBeforeDeductionScheduleAmount(scheduleAmount);
            if (scheduleAmount.compareTo(motorEngineerDetailsDto.getDeductions()) < 0) {
                otherAmount = otherAmount.subtract(motorEngineerDetailsDto.getDeductions().subtract(scheduleAmount));
                if (BigDecimal.ZERO.compareTo(otherAmount) > 0) {
                    throw new WrongValueException(AppConstant.ASSESSOR_PAYMENT_DEDUCTION, "Deductions can not be exceed total fee");
                }
                scheduleAmount = BigDecimal.ZERO;
            } else {
                scheduleAmount = scheduleAmount.subtract(motorEngineerDetailsDto.getDeductions());
            }
            assessorPaymentDeductionDetailDto.setDeduction(motorEngineerDetailsDto.getDeductions());
            assessorPaymentDeductionDetailDto.setOtherAmount(otherAmount);
            assessorPaymentDeductionDetailDto.setScheduleAmount(scheduleAmount);
            assessorPaymentDeductionDetailDto.setInputdatetime(Utility.sysDateTime());
            assessorPaymentDeductionDetailDto.setClaimNo(motorEngineerDetailsDto.getClaimNo());
            assessorPaymentDeductionDetailDto.setRefNo(motorEngineerDetailsDto.getRefNo());


            if (null != approveAssessorPaymentClaimWiseDto) {

                approveAssessorPaymentClaimWiseDto.setTotalOtherAmount(otherAmount.add(approveAssessorPaymentClaimWiseDto.getTotalOtherAmount()));
                approveAssessorPaymentClaimWiseDto.setTotalScheduleAmount(scheduleAmount.add(approveAssessorPaymentClaimWiseDto.getTotalScheduleAmount()));

                approveAssessorPaymentClaimWiseDao.updateByclaimNoAndAssessorCode(connection, approveAssessorPaymentClaimWiseDto);
            } else {

                approveAssessorPaymentClaimWiseDto = new ApproveAssessorPaymentClaimWiseDto();

                approveAssessorPaymentClaimWiseDto.setClaimNo(motorEngineerDetailsDto.getClaimNo());
                approveAssessorPaymentClaimWiseDto.setAssessorCode(code);
                approveAssessorPaymentClaimWiseDto.setRefNo(motorEngineerDetailsDto.getRefNo());
                approveAssessorPaymentClaimWiseDto.setTotalOtherAmount(otherAmount);
                approveAssessorPaymentClaimWiseDto.setTotalScheduleAmount(scheduleAmount);

                approveAssessorPaymentClaimWiseDao.insertMaster(connection, approveAssessorPaymentClaimWiseDto);
            }

            approveAssessorPaymentClaimWiseDto.setRefNo(motorEngineerDetailsDto.getRefNo());
            approveAssessorPaymentClaimWiseDto.setAction(AppConstant.PAYMENT_APPROVE);
            approveAssessorPaymentClaimWiseDto.setInputDatetime(Utility.sysDateTime());
            approveAssessorPaymentClaimWiseDao.insertHistory(connection, approveAssessorPaymentClaimWiseDto);

            offlineReserveAssessorDto.setOnOtherAmt(approveAssessorPaymentClaimWiseDto.getTotalOtherAmount());
            offlineReserveAssessorDto.setOnScheduleAmt(approveAssessorPaymentClaimWiseDto.getTotalScheduleAmount());

            if (assessorPaymentDeductionDetailDao.isAlreadySaved(connection, motorEngineerDetailsDto.getRefNo())) {
                assessorPaymentDeductionDetailDao.updateMaster(connection, assessorPaymentDeductionDetailDto);
            } else {
                assessorPaymentDeductionDetailDao.insertMaster(connection, assessorPaymentDeductionDetailDto);
            }
            assessorPaymentDeductionDetailDao.insertHistory(connection, assessorPaymentDeductionDetailDto);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return offlineReserveAssessorDto;
    }

    private BigDecimal getScheduleAmount(int inspectionId, int refNo, Integer jobType) throws Exception {
        BigDecimal scheduleAmount = BigDecimal.ZERO;
        try {
            ClaimInspectionTypeDto claimInspectionTypeDto = inspectionDetailsService.getInspectionTypeDto(String.valueOf(inspectionId));
            String assessorType = inspectionDetailsService.getAssessorTypeByRefNo(String.valueOf(refNo));
            if ("1".equals(jobType.toString())) { //DAY
                if (AppConstant.INHOUSE.equalsIgnoreCase(assessorType)) {
                    scheduleAmount = claimInspectionTypeDto.getInhouseDayDutyAmt();
                } else if (AppConstant.FREELANCE.equalsIgnoreCase(assessorType)) {
                    scheduleAmount = claimInspectionTypeDto.getFreelanceDayDutyAmt();
                } else {
                    scheduleAmount = claimInspectionTypeDto.getPermanantDayDutyAmt();
                }
            } else if ("2".equals(jobType.toString())) { //NIGHT
                if (AppConstant.INHOUSE.equalsIgnoreCase(assessorType)) {
                    scheduleAmount = claimInspectionTypeDto.getInhouseNightDutyAmt();
                } else if (AppConstant.FREELANCE.equalsIgnoreCase(assessorType)) {
                    scheduleAmount = claimInspectionTypeDto.getFreelanceNightDutyAmt();
                } else {
                    scheduleAmount = claimInspectionTypeDto.getPermanantNightDutyAmt();
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }
        return scheduleAmount;
    }

    private BigDecimal getOtherAmount(Integer refNo, BigDecimal otherFee, Integer mileage, BigDecimal costOfCall) {
        BigDecimal totalOtherAmount = BigDecimal.ZERO;
        try {
            String assessorType = inspectionDetailsService.getAssessorTypeByRefNo(refNo.toString());
            BigDecimal mileageFee = inspectionDetailsService.getMileageFee(assessorType);
            BigDecimal otherAmount = null == otherFee ? BigDecimal.ZERO : otherFee;
            BigDecimal mileageCost = new BigDecimal((null == mileage ? AppConstant.ZERO_INT : mileage)).multiply(mileageFee);
            BigDecimal costOFCall = null == costOfCall ? BigDecimal.ZERO : costOfCall;

            totalOtherAmount = otherAmount.add(mileageCost).add(costOFCall);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return totalOtherAmount;
    }

    private void offlineReserveClaim(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, BigDecimal reserveAmount, Integer lossType, UserDto user, String inspectionType) throws Exception {
        try {
            ClaimsDto claimsDto = callCenterDao.searchMaster(connection, motorEngineerDetailsDto.getClaimNo());
//            PolicyDto policyDto = policyDao.searchMaster(connection, claimsDto.getPolRefNo());
            McmsClaimOfflineReserveClaimDto mcmsClaimOfflineReserveClaimDto = new McmsClaimOfflineReserveClaimDto();
            String lossCode = commonUtilDao.findOne(connection, "claim_loss_type", "V_LOSS_CODE", "N_ID=" + lossType);
            if (null != claimsDto) {
                mcmsClaimOfflineReserveClaimDto.setOvClaimNo(claimsDto.getIsfClaimNo());
                mcmsClaimOfflineReserveClaimDto.setOnBillAmount(reserveAmount);
                mcmsClaimOfflineReserveClaimDto.setOnAllowedAmount(reserveAmount);
                mcmsClaimOfflineReserveClaimDto.setOnDepPer(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOnPaEstimateAmount(reserveAmount);
                mcmsClaimOfflineReserveClaimDto.setOvReportType(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvIdentificationNo(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssessment(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAppointment(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOvType(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssessment(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOvPanelCategory(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvInstitutionBranch(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvInstitutionCode(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvIdentificationCode(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvPanelType(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOdReceivedDate(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOdReceivedDate(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOvLossType(null != lossCode ? lossCode : AppConstant.LOSS_TYPE);
                mcmsClaimOfflineReserveClaimDto.setCvRequired(AppConstant.STRING_EMPTY);
                mcmsClaimOfflineReserveClaimDto.setOvRiskNo(Integer.toString(1));
                mcmsClaimOfflineReserveClaimDto.setDInsertDateTime(Utility.sysDateTime());
                mcmsClaimOfflineReserveClaimDto.setNRetryAttempt(AppConstant.ZERO_INT);
                mcmsClaimOfflineReserveClaimDto.setVIsfsUpdateStat(AppConstant.NO);
                mcmsClaimOfflineReserveClaimDto.setDIsfsUpdateDateTime(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssesSub(AppConstant.DEFAULT_DATE_TIME);
                mcmsClaimOfflineReserveClaimDto.setClaimNo(motorEngineerDetailsDto.getClaimNo());
                mcmsClaimOfflineReserveClaimDto.setPolicyChannelType(claimsDto.getPolicyChannelType());
                offlineReserveClaimDao.insertMaster(connection, mcmsClaimOfflineReserveClaimDto);
                saveClaimsLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, "Claim Reserve Changed [".concat(motorEngineerDetailsDto.getAssessorAllocationDto().getJobId() + " - " + inspectionType).concat("] RTE "), "Total Reserve Amount : " + reserveAmount);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }


    }

    private void manageAssessorPayement(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, UserDto user) throws Exception {
        try {
            AssessorPaymentDetailsDto searchAssessorPaymentDetailsDto = assessorPaymentDetailsDao.searchByKeyAndType(connection, motorEngineerDetailsDto.getRefNo(), AppConstant.ASSESSOR_PAYMENT);
            AssessorPaymentDetailsDto assessorPaymentDetailsDto = this.getAssessorPaymentDetails(connection, motorEngineerDetailsDto, user);
            if (null == searchAssessorPaymentDetailsDto) {
                assessorPaymentDetailsDao.insertMaster(connection, assessorPaymentDetailsDto);
            } else {
                assessorPaymentDetailsDao.updateStausByIdAndStatus(connection, searchAssessorPaymentDetailsDto.getTxnId(), assessorPaymentDetailsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception(e);
        }
    }

    private boolean isAvailableGarageInspection(Connection connection, Integer claimNo, Integer inspectionId) throws Exception {
        boolean isGarage = false;
        try {
            List<MotorEngineerDetailsDto> motorEngineerDetailsDtos = motorEngineerDetailsDao.searchByClaimNo(connection, claimNo);

            if (AppConstant.ON_SITE_INSPECTION == inspectionId || AppConstant.OFF_SITE_INSPECTION == inspectionId) {
                for (MotorEngineerDetailsDto motorEngineerDetailsDto : motorEngineerDetailsDtos) {
                    if ("A".equals(motorEngineerDetailsDto.getInspectionDetailsAuthStatus())) {
                        if (null != motorEngineerDetailsDto.getInspectionDto() && motorEngineerDetailsDto.getInspectionDto().getInspectionId() == AppConstant.GARAGE_INSPECTION) {
                            isGarage = true;
                            break;

                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return isGarage;
    }

    private void updateDefineDocument(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, Integer lossType, UserDto user) throws Exception {
        try {
            List<ClaimDocumentLossTypeOldDto> claimDocumentLossTypeOldDtoList = claimDocumentDao.allClaimDocumentLossTypeOldByClaimNo(connection, motorEngineerDetailsDto.getClaimNo());

            List<ClaimWiseDocumentDto> claimWiseDocumentDtoList = claimWiseDocumentDao.searchAllByClaimNo(connection, motorEngineerDetailsDto.getClaimNo());

            if (("1".equals(motorEngineerDetailsDto.getGarageInspectionDetailsDto().getSettlementMethod())) || ("2".equals(motorEngineerDetailsDto.getGarageInspectionDetailsDto().getSettlementMethod())) || ("1".equals(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getSettlementMethod())) || ("2".equals(motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getSettlementMethod()))) {
                if (lossType == 1) {
//                claimWiseDocumentDao.updateDocumentListTypeN(connection, motorEngineerDetailsDto.getClaimNo(), AppConstant.NO);

                    if (!claimDocumentLossTypeOldDtoList.isEmpty()) {
                        for (ClaimDocumentLossTypeOldDto wiseDocumentDto : claimDocumentLossTypeOldDtoList) {
                            ClaimWiseDocumentDto claimWiseDocumentDto = new ClaimWiseDocumentDto();
                            claimWiseDocumentDto.setDocumentTypeId(wiseDocumentDto.getDocumentTypeId());
                            claimWiseDocumentDto.setClaimNo(wiseDocumentDto.getClaimNo());
                            claimWiseDocumentDto.setIsMandatory(wiseDocumentDto.getIsMandatory());
                            claimWiseDocumentDto.setInpUserId(user.getUserId());
                            claimWiseDocumentDto.setInpDateTime(Utility.sysDateTime());
                            claimWiseDocumentDao.updateByClaimNoAndDocumentTypeId(connection, claimWiseDocumentDto);
                        }

                        for (ClaimWiseDocumentDto wiseDocumentDto : claimWiseDocumentDtoList) {
                            ClaimDocumentLossTypeOldDto claimDocumentLossTypeOldDto = new ClaimDocumentLossTypeOldDto();
                            claimDocumentLossTypeOldDto.setDocumentTypeId(wiseDocumentDto.getDocumentTypeId());
                            claimDocumentLossTypeOldDto.setClaimNo(wiseDocumentDto.getClaimNo());
                            claimDocumentLossTypeOldDto.setIsMandatory(wiseDocumentDto.getIsMandatory());
                            claimDocumentLossTypeOldDto.setOldStatus("TotalLoss");
                            claimDocumentLossTypeOldDto.setInpUser(user.getUserId());
                            claimDocumentLossTypeOldDto.setInpDateTime(Utility.sysDateTime());
                            claimDocumentDao.updateClaimDocumentLossTypeOld(connection, claimDocumentLossTypeOldDto);
                        }

                    } else {
                        for (ClaimWiseDocumentDto wiseDocumentDto : claimWiseDocumentDtoList) {
                            ClaimDocumentLossTypeOldDto claimDocumentLossTypeOldDto = new ClaimDocumentLossTypeOldDto();
                            claimDocumentLossTypeOldDto.setDocumentTypeId(wiseDocumentDto.getDocumentTypeId());
                            claimDocumentLossTypeOldDto.setClaimNo(wiseDocumentDto.getClaimNo());
                            claimDocumentLossTypeOldDto.setIsMandatory(wiseDocumentDto.getIsMandatory());
                            claimDocumentLossTypeOldDto.setOldStatus("TotalLoss");
                            claimDocumentLossTypeOldDto.setInpUser(user.getUserId());
                            claimDocumentLossTypeOldDto.setInpDateTime(Utility.sysDateTime());
                            claimDocumentDao.insertClaimDocumentLossTypeOld(connection, claimDocumentLossTypeOldDto);

                        }
                        ClaimWiseDocumentDto wiseDocumentDto = new ClaimWiseDocumentDto();
                        wiseDocumentDto.setIsMandatory(AppConstant.YES);
                        wiseDocumentDto.setClaimNo(motorEngineerDetailsDto.getClaimNo());
                        wiseDocumentDto.setInpUserId(user.getUserId());
                        wiseDocumentDto.setInpDateTime(Utility.sysDateTime());
                        claimWiseDocumentDao.updateByClaimNoAndDocumentTypeIdInPartialLoss(connection, wiseDocumentDto);
                    }

                } else {
//                    claimWiseDocumentDao.updateDocumentListTypeN(connection, motorEngineerDetailsDto.getClaimNo(), AppConstant.NO);
                    if (!claimDocumentLossTypeOldDtoList.isEmpty()) {
                        for (ClaimDocumentLossTypeOldDto wiseDocumentDto : claimDocumentLossTypeOldDtoList) {
                            ClaimWiseDocumentDto claimWiseDocumentDto = new ClaimWiseDocumentDto();
                            claimWiseDocumentDto.setDocumentTypeId(wiseDocumentDto.getDocumentTypeId());
                            claimWiseDocumentDto.setClaimNo(wiseDocumentDto.getClaimNo());
                            claimWiseDocumentDto.setIsMandatory(wiseDocumentDto.getIsMandatory());
                            claimWiseDocumentDto.setInpUserId(user.getUserId());
                            claimWiseDocumentDto.setInpDateTime(Utility.sysDateTime());
                            claimWiseDocumentDao.updateByClaimNoAndDocumentTypeId(connection, claimWiseDocumentDto);
                        }

                        for (ClaimWiseDocumentDto wiseDocumentDto : claimWiseDocumentDtoList) {
                            ClaimDocumentLossTypeOldDto claimDocumentLossTypeOldDto = new ClaimDocumentLossTypeOldDto();
                            claimDocumentLossTypeOldDto.setDocumentTypeId(wiseDocumentDto.getDocumentTypeId());
                            claimDocumentLossTypeOldDto.setClaimNo(wiseDocumentDto.getClaimNo());
                            claimDocumentLossTypeOldDto.setIsMandatory(wiseDocumentDto.getIsMandatory());
                            claimDocumentLossTypeOldDto.setOldStatus(wiseDocumentDto.getIsPartialLoss());
                            claimDocumentLossTypeOldDto.setInpUser(user.getUserId());
                            claimDocumentLossTypeOldDto.setInpDateTime(Utility.sysDateTime());
                            claimDocumentDao.updateClaimDocumentLossTypeOld(connection, claimDocumentLossTypeOldDto);
                        }


                    } else {
                        for (ClaimWiseDocumentDto wiseDocumentDto : claimWiseDocumentDtoList) {
                            ClaimDocumentLossTypeOldDto claimDocumentLossTypeOldDto = new ClaimDocumentLossTypeOldDto();
                            claimDocumentLossTypeOldDto.setDocumentTypeId(wiseDocumentDto.getDocumentTypeId());
                            claimDocumentLossTypeOldDto.setClaimNo(wiseDocumentDto.getClaimNo());
                            claimDocumentLossTypeOldDto.setIsMandatory(wiseDocumentDto.getIsMandatory());
                            claimDocumentLossTypeOldDto.setOldStatus("PartialLoss");
                            claimDocumentLossTypeOldDto.setInpUser(user.getUserId());
                            claimDocumentLossTypeOldDto.setInpDateTime(Utility.sysDateTime());
                            claimDocumentDao.insertClaimDocumentLossTypeOld(connection, claimDocumentLossTypeOldDto);
                        }

//                        claimWiseDocumentDao.updateTotalLossDocumentList(connection, motorEngineerDetailsDto.getClaimNo(), AppConstant.YES);

                    }

                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void reassignAsPartialLossTeam(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, ClaimHandlerDto searchedClaimHandlerDto, String url, UserDto user) throws Exception {
        String userId;
        String notificationMsg;
        String task;
        try {
            if (searchedClaimHandlerDto.getLossType().equals(AppConstant.TOTAL_LOSS_TYPE)) {
                notificationMsg = "You have received Total loss claim converted as partial loss";
                task = "Total loss claim converted as Partial Loss ";
            } else {
                notificationMsg = "You have received Offer Team partial loss claim convert as partial loss";
                task = "Offer Team partial loss claim convert as partial loss ";
            }
            saveClaimsLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, "Claim Converted", task);
            if (null == searchedClaimHandlerDto.getInitLiabilityAprvStatus() || searchedClaimHandlerDto.getInitLiabilityAprvStatus().equalsIgnoreCase(AppConstant.STRING_PENDING)) {
                userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), motorEngineerDetailsDto.getClaimNo());
                assignInitialLiability(connection, motorEngineerDetailsDto.getClaimNo(), notificationMsg, task, userId, url, user);
            } else {
                userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), motorEngineerDetailsDto.getClaimNo());
                assignClaimHandler(connection, motorEngineerDetailsDto.getClaimNo(), searchedClaimHandlerDto.getIsFileStore(), notificationMsg, task, userId, url, user, false);
                if (searchedClaimHandlerDto.getIsProvideOffer().equalsIgnoreCase(AppConstant.YES)) {
                    updateSpTeamAndMofaTeam(connection, motorEngineerDetailsDto.getClaimNo(), notificationMsg, user);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void reassignAsTotalLossTeam(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, ClaimHandlerDto searchedClaimHandlerDto, String url, UserDto user) throws Exception {
        String userId;
        String notificationMsg;
        String task;
        int status;
        try {
            status = claimHandlerDao.getClaimStatusByClaimNo(connection, searchedClaimHandlerDto.getClaimNo());
            if (searchedClaimHandlerDto.getIsProvideOffer().equalsIgnoreCase(AppConstant.YES)) {
                notificationMsg = "You have received Offer Team partial loss claim converted as total loss";
                task = "Offer Team Partial Loss claim converted as Total Loss ";
            } else {
                notificationMsg = "You have received partial loss claim converted as total loss";
                task = "Partial Loss claim converted as Total Loss ";
            }
            saveClaimsLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, "Claim Converted", task);
            if (null == searchedClaimHandlerDto.getInitLiabilityAprvStatus() || searchedClaimHandlerDto.getInitLiabilityAprvStatus().equalsIgnoreCase(AppConstant.STRING_PENDING)) {
                userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL__TOTAL_LOSS_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), motorEngineerDetailsDto.getClaimNo());
                assignTotalLossInitLiabilityAndClaimHandler(connection, motorEngineerDetailsDto.getClaimNo(), notificationMsg, task, userId, url, user);
            } else {
                userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL__TOTAL_LOSS_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), motorEngineerDetailsDto.getClaimNo());
                assignClaimHandler(connection, motorEngineerDetailsDto.getClaimNo(), searchedClaimHandlerDto.getIsFileStore(), notificationMsg, task, userId, url, user, false);
                if (null != searchedClaimHandlerDto.getLiabilityAprvStatus() && searchedClaimHandlerDto.getLiabilityAprvStatus().equals(AppConstant.APPROVE)) {
                    if (status == AppConstant.CLAIM_STATUS_CLAIM_HANDLER_SPECIAL_COMMENT) {
                        claimHandlerDao.updateOldClaimStatus(connection, motorEngineerDetailsDto.getClaimNo(), 50);
                    } else {
                        claimHandlerDao.updateClaimStatus(connection, motorEngineerDetailsDto.getClaimNo(), 50);
                    }
                } else if (status != AppConstant.CLAIM_STATUS_FORWARD_TO_DECISION_MAKER && status != AppConstant.CLAIM_STATUS_DECISION_MAKER_REQUEST_INVESTIGATION && status != AppConstant.CLAIM_STATUS_DECISION_MAKER_INVESTIGATION_ARRANGED
//                        && status != AppConstant.CLAIM_STATUS_INVESTIGATION_COMPLETED
                        && status != AppConstant.CLAIM_STATUS_INVESTIGATION_CANCEL && status != AppConstant.CLAIM_STATUS_CLAIM_HANDLER_REQUEST_INVESTIGATION && status != AppConstant.CLAIM_STATUS_INVESTIGATION_REQUEST_APPROVED) {
                    if (status == AppConstant.CLAIM_STATUS_CLAIM_HANDLER_SPECIAL_COMMENT) {
                        claimHandlerDao.updateOldClaimStatus(connection, motorEngineerDetailsDto.getClaimNo(), 49);
                    } else {
                        claimHandlerDao.updateClaimStatus(connection, motorEngineerDetailsDto.getClaimNo(), 49);
                    }
                }
                if (searchedClaimHandlerDto.getIsProvideOffer().equalsIgnoreCase(AppConstant.YES)) {
                    updateSpTeamAndMofaTeam(connection, motorEngineerDetailsDto.getClaimNo(), notificationMsg, user);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void updateSpTeamAndMofaTeam(Connection connection, int claimNo, String notificationMsg, UserDto user) throws Exception {
        String url = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        try {
            List<ClaimCalculationSheetMainDto> list = claimCalculationSheetMainDao.searchByClaimNoAndNotInVoucherGeneratedOrPending(connection, claimNo);
            for (ClaimCalculationSheetMainDto claimCalculationSheetMainDto : list) {
                ClaimCalculationSheetMainDto updateCalculationShettMainDto = new ClaimCalculationSheetMainDto();
                String specialTeamUserId = null;
                String mofaUserId = null;
                updateCalculationShettMainDto.setCalSheetId(claimCalculationSheetMainDto.getCalSheetId());
                if (null != claimCalculationSheetMainDto.getSpecialTeamAssignUserId() && !claimCalculationSheetMainDto.getSpecialTeamAssignUserId().isEmpty()) {
                    specialTeamUserId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_SPECIAL_TEAM, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                    updateCalculationShettMainDto.setSpecialTeamAssignUserId(specialTeamUserId);
                    updateCalculationShettMainDto.setSpecialTeamAssignDateTime(Utility.sysDateTime());
                }

                if (null != claimCalculationSheetMainDto.getSpecialTeamMofaAssignUserId() && !claimCalculationSheetMainDto.getSpecialTeamMofaAssignUserId().isEmpty()) {
                    mofaUserId = getMofaUser(connection, claimNo, user, claimCalculationSheetMainDto, AppConstant.ACCESS_LEVEL_MOFA_TEAM);
                    updateCalculationShettMainDto.setSpecialTeamMofaAssignUserId(mofaUserId);
                    updateCalculationShettMainDto.setSpecialTeamMofaAssignDateTime(Utility.sysDateTime());
                }

                claimCalculationSheetMainDao.assignSpTeamAndMofaTeam(connection, updateCalculationShettMainDto);

                if (claimCalculationSheetMainDto.getStatus() == 63) {
                    saveNotification(connection, claimNo, user.getUserId(), specialTeamUserId, notificationMsg, url);
                    saveClaimsLogs(connection, claimNo, user, "Special Team User Changed", "Special Team User Changed To -> " + specialTeamUserId);
                } else if (claimCalculationSheetMainDto.getStatus() == 64) {
                    saveNotification(connection, claimNo, user.getUserId(), mofaUserId, notificationMsg, url);
                    saveClaimsLogs(connection, claimNo, user, "Special Team Mofa User Changed", "Special Team Mofa User Changed To -> " + mofaUserId);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }


    }

    private void updateOfferSpTeamAndOfferMofaTeam(Connection connection, int claimNo, String notificationMsg, UserDto user) throws Exception {
        String url = AppConstant.CLAIM_HANDLER_VIEW.concat("?P_N_CLIM_NO=").concat(Integer.toString(claimNo)).concat("&P_TAB_INDEX=").concat(String.valueOf(15));
        try {
            List<ClaimCalculationSheetMainDto> list = claimCalculationSheetMainDao.searchByClaimNoAndNotInVoucherGeneratedOrPending(connection, claimNo);
            for (ClaimCalculationSheetMainDto claimCalculationSheetMainDto : list) {
                ClaimCalculationSheetMainDto updateCalculationShettMainDto = new ClaimCalculationSheetMainDto();
                String specialTeamUserId = null;
                String mofaUserId = null;
                updateCalculationShettMainDto.setCalSheetId(claimCalculationSheetMainDto.getCalSheetId());
                if (null != claimCalculationSheetMainDto.getSpecialTeamAssignUserId() && !claimCalculationSheetMainDto.getSpecialTeamAssignUserId().isEmpty()) {

                    specialTeamUserId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo);
                    updateCalculationShettMainDto.setSpecialTeamAssignUserId(specialTeamUserId);
                    updateCalculationShettMainDto.setSpecialTeamAssignDateTime(Utility.sysDateTime());

                }

                if (null != claimCalculationSheetMainDto.getSpecialTeamMofaAssignUserId() && !claimCalculationSheetMainDto.getSpecialTeamMofaAssignUserId().isEmpty()) {

                    mofaUserId = getMofaUser(connection, claimNo, user, claimCalculationSheetMainDto, AppConstant.ACCESS_LEVEL_OFFER_TEAM_MOFA_TEAM);
                    updateCalculationShettMainDto.setSpecialTeamMofaAssignUserId(mofaUserId);
                    updateCalculationShettMainDto.setSpecialTeamMofaAssignDateTime(Utility.sysDateTime());

                }

                claimCalculationSheetMainDao.assignSpTeamAndMofaTeam(connection, updateCalculationShettMainDto);

                if (claimCalculationSheetMainDto.getStatus() == 63) {
                    saveNotification(connection, claimNo, user.getUserId(), specialTeamUserId, notificationMsg, url);
                    saveClaimsLogs(connection, claimNo, user, "Special Team User Changed", "Special Team User Changed To -> " + specialTeamUserId);
                } else if (claimCalculationSheetMainDto.getStatus() == 64) {
                    saveNotification(connection, claimNo, user.getUserId(), mofaUserId, notificationMsg, url);
                    saveClaimsLogs(connection, claimNo, user, "Special Team Mofa User Changed", "Special Team Mofa User Changed To -> " + mofaUserId);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }


    }

    private String getMofaUser(Connection connection, int claimNo, UserDto user, ClaimCalculationSheetMainDto claimCalculationSheetMainDto, int accessUserType) throws Exception {
        String mofaUserId;
        try {
            UserAuthorityLimitDto userAuthorityLimitDto = userAuthorityLimitDao.getUserAuthorityDetailsByMofaDetails(connection, claimCalculationSheetMainDto.getCalSheetId(), claimCalculationSheetMainDto.getSpecialTeamMofaAssignUserId(), AppConstant.MOFA_TEAM_DEPARTMENT_ID);
            mofaUserId = claimUserAllocationService.getNextAuthLimitAssignUser(connection, accessUserType, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), claimNo, true, claimCalculationSheetMainDto.getPayableAmount(), userAuthorityLimitDto.getToLimit(), 0, false);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return mofaUserId;
    }

    private void assignClaimHandler(Connection connection, int claimNo, String isFileStore, String notificationMsg, String task, String userId, String url, UserDto user, boolean isDmakerAssignedAndOffer) throws Exception {
        try {
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setAssignUserId(userId);
            claimHandlerDao.updateClaimAssignUser(connection, claimHandlerDto);
            claimCalculationSheetMainDao.updateCalsheetAssignUser(connection, userId, claimNo);

            saveClaimProcessFlow(connection, claimNo, 0, task, user.getUserId(), Utility.sysDateTime(), userId, AppConstant.YES);
            saveClaimsLogs(connection, claimNo, user, "Claim Handler User Changed", "Claim Handler User Changed To -> " + userId);
            if (!isDmakerAssignedAndOffer) {
                saveNotification(connection, claimNo, user.getUserId(), userId, notificationMsg, url);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void assignInitialLiability(Connection connection, int claimNo, String notificationMsg, String task, String userId, String url, UserDto user) throws Exception {
        int status;
        try {
            status = claimHandlerDao.getClaimStatusByClaimNo(connection, claimNo);
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setInitLiabilityAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setInitLiabilityAprvStatus(AppConstant.STRING_PENDING);
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setInitLiabilityAssignUserId(userId);
            claimHandlerDao.updateInitialLiabilityUserLiabilityPending(connection, claimHandlerDto);
            if (status == AppConstant.CLAIM_STATUS_CLAIM_HANDLER_SPECIAL_COMMENT) {
                claimHandlerDao.updateOldClaimStatus(connection, claimNo, 35);
            } else {
                claimHandlerDao.updateClaimStatus(connection, claimNo, 35);
            }
            saveNotification(connection, claimNo, user.getUserId(), userId, notificationMsg, url);
            saveClaimProcessFlow(connection, claimNo, 35, task, user.getUserId(), Utility.sysDateTime(), userId, AppConstant.YES);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }

    }

    private void assignTotalLossInitLiabilityAndClaimHandler(Connection connection, int claimNo, String notificationMsg, String task, String userId, String url, UserDto user) throws Exception {
        int status;
        try {
            status = claimHandlerDao.getClaimStatusByClaimNo(connection, claimNo);
            ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
            claimHandlerDto.setInitLiabilityAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setInitLiabilityAprvStatus(AppConstant.STRING_PENDING);
            claimHandlerDto.setClaimNo(claimNo);
            claimHandlerDto.setInitLiabilityAssignUserId(userId);
            claimHandlerDto.setAssignDateTime(Utility.sysDateTime());
            claimHandlerDto.setAssignUserId(userId);
            claimHandlerDao.updateInitialLiabilityUserAndAssignUser(connection, claimHandlerDto);
            if (status == AppConstant.CLAIM_STATUS_CLAIM_HANDLER_SPECIAL_COMMENT) {
                claimHandlerDao.updateOldClaimStatus(connection, claimNo, 35);
            } else {
                claimHandlerDao.updateClaimStatus(connection, claimNo, 35);
            }
            saveNotification(connection, claimNo, user.getUserId(), userId, notificationMsg, url);
            saveClaimProcessFlow(connection, claimNo, 35, task, user.getUserId(), Utility.sysDateTime(), userId, AppConstant.YES);
            saveClaimsLogs(connection, claimNo, user, "Claim Handler User Changed", "Claim Handler User Changed To -> " + userId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }

    }

    private void assignPartialLossInitLiability(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, String url, UserDto user) throws Exception {
        String task;
        String notificationMsg;
        task = "Assign to initial liability user";
        notificationMsg = "You have received a new claim file ";
        try {
            String userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), motorEngineerDetailsDto.getClaimNo());
            assignInitialLiability(connection, motorEngineerDetailsDto.getClaimNo(), notificationMsg, task, userId, url, user);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void assignOfferTeamPartialLossInitLiability(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, int offerOrLossType, String url, UserDto user) throws Exception {
        String task;
        String notificationMsg;
        String offerName = getOfferName(offerOrLossType);
        task = "Assign to Offer Team initial liability user ".concat(offerName);
        notificationMsg = "You have received a new claim file with offer calculation sheet";
        try {
            String userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), motorEngineerDetailsDto.getClaimNo());
            assignInitialLiability(connection, motorEngineerDetailsDto.getClaimNo(), notificationMsg, task, userId, url, user);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void assignTotalLossInitLiabilityAndClaimHandler(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, String url, UserDto user) throws Exception {
        String task;
        String notificationMsg;
        task = "Assign to Total Loss initial liability user";
        notificationMsg = "You have received a new claim file ";
        try {
            String userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL__TOTAL_LOSS_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), motorEngineerDetailsDto.getClaimNo());
            assignTotalLossInitLiabilityAndClaimHandler(connection, motorEngineerDetailsDto.getClaimNo(), notificationMsg, task, userId, url, user);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private void reassignAsOfferTeam(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, ClaimHandlerDto searchedClaimHandlerDto, int offerOrLossType, String url, UserDto user) throws Exception {
        String userId;
        String notificationMsg;
        String task;
        try {
            String offerName = getOfferName(offerOrLossType);
            if (searchedClaimHandlerDto.getLossType().equals(AppConstant.TOTAL_LOSS_TYPE)) {
                notificationMsg = "You have received Total loss claim converted as Offer Team Partial loss";
                task = "Offer Team Total Loss claim converted as Offer Team Partial Loss ".concat(offerName);
            } else {
                notificationMsg = "You have received partial loss claim converted as Offer Team Partial loss";
                task = "Partial Loss claim converted as Offer Team Partial Loss ".concat(offerName);
            }
            saveClaimsLogs(connection, motorEngineerDetailsDto.getClaimNo(), user, "Claim Converted", task);
            if (null == searchedClaimHandlerDto.getInitLiabilityAprvStatus() || searchedClaimHandlerDto.getInitLiabilityAprvStatus().equalsIgnoreCase(AppConstant.STRING_PENDING)) {
                userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), motorEngineerDetailsDto.getClaimNo());
                assignInitialLiability(connection, motorEngineerDetailsDto.getClaimNo(), notificationMsg, task, userId, url, user);
            } else {
                userId = claimUserAllocationService.getNextAssignUser(connection, AppConstant.ACCESS_LEVEL_OFFER_TEAM_CLAIM_HANDLER, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, AppConstant.ASSIGN, user.getUserId(), motorEngineerDetailsDto.getClaimNo());
                assignClaimHandler(connection, motorEngineerDetailsDto.getClaimNo(), searchedClaimHandlerDto.getIsFileStore(), notificationMsg, task, userId, url, user, searchedClaimHandlerDto.getClaimStatus().equals(AppConstant.CLAIM_STATUS_FORWARD_TO_DECISION_MAKER));
                updateOfferSpTeamAndOfferMofaTeam(connection, motorEngineerDetailsDto.getClaimNo(), notificationMsg, user);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    private String getOfferName(int offerOrLossType) {
        if (AppConstant.ONSITE_OFFER_TYPE.equals(offerOrLossType)) {
            return "(On-site Offer)";
        } else if (AppConstant.GARAGE_OFFER_TYPE.equals(offerOrLossType)) {
            return "(Garage Offer)";
        } else if (AppConstant.DESKTOP_OFFER_TYPE.equals(offerOrLossType)) {
            return "(Desktop Offer)";
        }
        return AppConstant.STRING_EMPTY;
    }

    public void saveConfirmationLogs(Connection connection, Integer claimNo, UserDto user, BigDecimal outstandingPremium, boolean isCancelledPolicy) throws Exception {
        boolean hasOutstandingPremium = outstandingPremium.compareTo(BigDecimal.ZERO) > 0;
        try {
            if (isCancelledPolicy && hasOutstandingPremium) {
                saveClaimsLogs(connection, claimNo, user, "Cancelled Policy and Premium Outstanding Confirmation Alert", "Marked As Ok to \"This is a cancelled policy and has Rs." + outstandingPremium + " of outstanding premium amount\" alert");
            } else if (isCancelledPolicy) {
                saveClaimsLogs(connection, claimNo, user, "Cancelled Policy Confirmation Alert", "Marked As Ok to \"This is a cancelled policy\" alert");
            } else if (hasOutstandingPremium) {
                saveClaimsLogs(connection, claimNo, user, "Premium Outstanding Confirmation Alert", "Marked As Ok to \"This policy has Rs." + outstandingPremium + " of outstanding premium amount\" alert");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    private String getNextLevelRte(Connection connection, AuthAssignRteDto assignRteDto, Integer rteLevel, BigDecimal forwardAcr) throws Exception {
        String forwardUser = AppConstant.STRING_EMPTY;
        boolean isUnAuthOrLeave = false;
        try {
            if (null != assignRteDto) {
                if (null == rteLevel || rteLevel.equals(AppConstant.RTE_LEVEL_1)) {
                    isUnAuthOrLeave = claimUserAllocationDao.isRteLeave(connection, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, Utility.sysDateTime(), AppConstant.APPROVE, assignRteDto.getRteLvl2());
                    forwardUser = assignRteDto.getRteLvl2();
                }
                if (isUnAuthOrLeave || Objects.equals(rteLevel, AppConstant.RTE_LEVEL_2)) {
                    if (Objects.equals(rteLevel, AppConstant.RTE_LEVEL_2)) {
                        isUnAuthOrLeave = isUnAuthoritative(connection, forwardAcr, AppConstant.RTE_LEVEL_3) || claimUserAllocationDao.isRteLeave(connection, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, Utility.sysDateTime(), AppConstant.APPROVE, assignRteDto.getRteLvl3());
                    } else {
                        isUnAuthOrLeave = claimUserAllocationDao.isRteLeave(connection, AppConstant.CLAIM_HANDLER_NORAML_FUNCTION, Utility.sysDateTime(), AppConstant.APPROVE, assignRteDto.getRteLvl3());
                    }
                    forwardUser = assignRteDto.getRteLvl3();
                }
                if (isUnAuthOrLeave || Objects.equals(rteLevel, AppConstant.RTE_LEVEL_3)) {
                    forwardUser = assignRteDto.getRteLvl4();
                }
            } else {
                return AppConstant.STRING_EMPTY;
            }
            return forwardUser;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    private boolean isUnAuthoritative(Connection connection, BigDecimal forwardAcr, Integer level) throws Exception {
        try {
            UserAuthorityLimitDto userAuthorityLimitDto = userAuthorityLimitDao.searchByLevelCodeAndDepartmentId(connection, level, AppConstant.MOTOR_ENGINEER_DEPARTMENT_ID);
            if (null != forwardAcr && forwardAcr.compareTo(userAuthorityLimitDto.getToLimit()) > 0) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return false;
    }

    private void sendOfferSms(Connection connection, Integer claimNo, BigDecimal payableAmount, UserDto user) {
        try {
            ClaimsDto claim = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);
            ClaimHandlerDto claimHandlerDto = claimHandlerDao.searchMasterByClaimNo(connection, claimNo);
            if ((claimHandlerDto.getClaimStatus().equals(AppConstant.CLAIM_STATUS_INITIAL_LIABILITY_APPROVED) && claimHandlerDto.getIsFileStore().equalsIgnoreCase(AppConstant.YES)) || (claimHandlerDto.getClaimStatus().equals(AppConstant.CLAIM_STATUS_LIABILITY_APPROVED))) {
                List<String> smsParameterList = new ArrayList<>();
                smsParameterList.add(claim.getPolicyDto().getVehicleNumber());
                smsParameterList.add(String.valueOf(claim.getPolicyDto().getCustName()));
                smsParameterList.add(String.valueOf(claim.getAccidDate()));
                smsParameterList.add(String.valueOf(payableAmount));
                sendSmsMessage(connection, AppConstant.OFFER_SMS_TO_AGENT, smsParameterList, claim.getPolicyDto().getPolicySellingAgentDetailsDto().getContactNo(), claim.getPolicyDto().getPolicyChannelType(), user, claimNo, AppConstant.SEND_TO_AGENT);
                sendSmsMessage(connection, AppConstant.OFFER_SMS_TO_CUSTOMER, smsParameterList, claim.getPolicyDto().getCustMobileNo(), claim.getPolicyDto().getPolicyChannelType(), user, claimNo, AppConstant.SEND_TO_CUSTOMER);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void sendLargeClaimEmail(Connection connection, Integer claimNo, BigDecimal value, BigDecimal previousAcr, boolean isChangeRequest, UserDto user) throws Exception {
        boolean isCoInsOrFac = false;
        String emailSubject1 = AppConstant.STRING_EMPTY;
        String emailSubject2 = AppConstant.STRING_EMPTY;
        StringBuilder stringBuilder = new StringBuilder();
        Email email = new Email();
        boolean isSeperateMails = false;
        DecimalFormat decimalFormat = new DecimalFormat("#,###.00");
        String policyChannelType;

        try {
            List<LargeClaimEmailReceiversDto> automatedMailReceivers = largeClaimEmailDao.getAutomatedMailReceivers(connection);

            if (null != automatedMailReceivers && automatedMailReceivers.size() > 0) {
                BigDecimal authorityLimit = largeClaimEmailDao.getAuthorityLimits(connection, user.getAccessUserType());
                ClaimsDto claimsDto = callCenterService.getClaimsDtoByClaimNo(connection, claimNo);

                policyChannelType = null != claimsDto.getPolicyDto().getPolicyChannelType() && !claimsDto.getPolicyDto().getPolicyChannelType().isEmpty() && claimsDto.getPolicyDto().getPolicyChannelType().equals(PolicyChannelType.TAKAFUL.name()) ? AppConstant.URL_TYPE_TAKAFUL : AppConstant.URL_TYPE_CONVENTIONAL;

                LargeClaimDto largeClaimDetailsDto = restPolicyDetailsService.getLargeClaimDetailsDtoList(claimsDto.getPolicyDto(), policyChannelType);
                CoInsOrFacDetailDto coInsOrFacDetail = restPolicyDetailsService.getCoInsOrFacDetail(claimsDto.getPolicyDto(), policyChannelType);
                if (null != coInsOrFacDetail && null != coInsOrFacDetail.getClaimNo()) {
                    isCoInsOrFac = true;
                }

                boolean isHistoryRecord = largeClaimEmailDao.isMailAlreadySent(connection, claimNo);

                BigDecimal mnDivident = new BigDecimal("1000000.0");
                BigDecimal limitInMillions = authorityLimit.divide(mnDivident, 2, RoundingMode.HALF_UP);
                String formattedVal = String.format("%.2fMN", limitInMillions);

                //boolean acrChanged = previousAcr.equals(BigDecimal.ZERO) || value.compareTo(previousAcr) > 0;
                if (value.compareTo(authorityLimit) > 0 && isCoInsOrFac) {
                    isSeperateMails = true;
                    emailSubject1 = "LARGE CLAIM NOTIFICATION -  CLAIM NO-[" + claimNo + "] VEHICLE NO-[" + claimsDto.getPolicyDto().getVehicleNumber() + "] OVER " + formattedVal + " - RTE";
                    emailSubject2 = "RTE APPROVAL CO-INSURANCE & FAC INSURANCE ARRANGEMENTS POLICY";
                } else if (value.compareTo(authorityLimit) > 0 && !isChangeRequest || isChangeRequest && value.compareTo(previousAcr) != 0 && value.compareTo(authorityLimit) > 0) {
                    emailSubject1 = "LARGE CLAIM NOTIFICATION -  CLAIM NO-[" + claimNo + "] VEHICLE NO-[" + claimsDto.getPolicyDto().getVehicleNumber() + "] OVER " + formattedVal + " - RTE";
                } else if (isCoInsOrFac) {
                    emailSubject1 = "RTE APPROVAL CO-INSURANCE & FAC INSURANCE ARRANGEMENTS POLICY";
                } else {
                    return;
                }
//        } else {
//            if (value.compareTo(authorityLimit) > 0 && isCoInsOrFac) {
//                isSeperateMails = true;
//                emailSubject1 = isHistoryRecord ? "LARGE CLAIM NOTIFICATION - CLAIM NO-[" + claimNo + "] VEHICLE NO-[" + claimsDto.getPolicyDto().getVehicleNumber() + "] – OVER 3.5MN – FINAL AMOUNT EXCEEDED" : "LARGE CLAIM NOTIFICATION - CLAIM NO – VEHICLE NO – OVER 3.5MN – FINAL AMOUNT";
//                emailSubject2 = "TOTAL APPROVED CLAIM AMOUNT CO-INSURANCE FAC INSURANCE ARRANGEMENTS POLICY";
//            } else if (value.compareTo(authorityLimit) > 0) {
//                emailSubject1 = isHistoryRecord ? "LARGE CLAIM NOTIFICATION - CLAIM NO-[" + claimNo + "] VEHICLE NO-[" + claimsDto.getPolicyDto().getVehicleNumber() + "] – OVER 3.5MN – FINAL AMOUNT EXCEEDED" : "LARGE CLAIM NOTIFICATION - CLAIM NO – VEHICLE NO – OVER 3.5MN – FINAL AMOUNT";
//            } else if (isCoInsOrFac) {
//                emailSubject1 = "TOTAL APPROVED CLAIM AMOUNT CO-INSURANCE FAC INSURANCE ARRANGEMENTS POLICY";
//            } else {
//                return;
//            }
//        }

                MessageContentDetails messageContentDetails = emailDao.searchMessageContentDetail(connection, AppConstant.LARGE_CLAIM_MAIL);
                email.setEmailMassege(messageContentDetails.getMessageBody());

                ArrayList<String> list = new ArrayList<>();

                if (isHistoryRecord) {
                    List<LargeClaimEmailHistoryDto> historyRecords = largeClaimEmailDao.getHistoryRecords(connection, claimNo);
                    for (LargeClaimEmailHistoryDto emailHistoryDto : historyRecords) {
                        stringBuilder.append("<tr><td style=\"border: 1px solid black; text-align: right;\">");
                        stringBuilder.append(emailHistoryDto.getApprovedDate());
                        stringBuilder.append("</td><td style=\"border: 1px solid black; text-align: right;\">");
                        stringBuilder.append(decimalFormat.format(emailHistoryDto.getAcr()));
                        stringBuilder.append("</td><td style=\"border: 1px solid black; text-align: right;\">");
                        stringBuilder.append(emailHistoryDto.getCalsheetType());
                        stringBuilder.append("</td><td style=\"border: 1px solid black; text-align: right;\">");
                        stringBuilder.append(decimalFormat.format(emailHistoryDto.getTotalPayable()));
                        stringBuilder.append("</td></tr>");
                    }
                } else {
                    stringBuilder.append("<tr><td style=\"border: 1px solid black; text-align: right;\">N/A</td>");
                    stringBuilder.append("<td style=\"border: 1px solid black; text-align: right;\">N/A</td>");
                    stringBuilder.append("<td style=\"border: 1px solid black; text-align: right;\">N/A</td>");
                    stringBuilder.append("<td style=\"border: 1px solid black; text-align: right;\">N/A</td></tr>");
                }

                String policyDate = policyDao.getOldestPolicyInspecDate(connection, claimsDto.getPolicyDto().getPolicyNumber());

                list.add(String.valueOf(stringBuilder));

                list.add(Utility.sysDate(AppConstant.DATE_FORMAT));

                list.add("MOTOR");
                list.add(claimsDto.getPolicyDto().getPolicyNumber());
                list.add("MOTOR");//Class
                list.add(claimsDto.getPolicyDto().getCustName());
                list.add(claimsDto.getPolicyDto().getVehicleNumber());
                list.add(formatDate(policyDate) + "   -   " + formatDate(claimsDto.getPolicyDto().getExpireDate()));//Original Policy Period
                list.add("Rs." + decimalFormat.format(claimsDto.getPolicyDto().getSumInsured()));//Sum Insured
                list.add(String.valueOf(claimNo));
                list.add(String.valueOf(claimsDto.getIsfClaimNo()));
                list.add(formatDate(claimsDto.getAccidDate()));//Date of Loss
                list.add(formatDate(claimsDto.getDateOfReport()));//Date of Intimation
                String causeOfLoss = commonUtilDao.findOne(connection, "claim_cause_of_loss_type", "V_CAUSE_OF_LOSS", "N_ID=" + claimsDto.getCauseOfLoss());
                list.add(causeOfLoss);//Cause of Loss
                list.add("Rs." + decimalFormat.format(value));//Gross Reserve
                list.add("ACR Update");
                Integer maxInvestigationId = investigationDetailsDao.getMaxInvestigationTxnIdByClaimNo(connection, claimNo);
                InvestigationDetailsDto investigationDetailsDto = investigationDetailsDao.searchMaster(connection, maxInvestigationId);
                list.add(maxInvestigationId == AppConstant.ZERO_INT || null == investigationDetailsDto.getAssignInvestigatorName() ? "N/A" : investigationDetailsDto.getAssignInvestigatorName());//Investigation Appointment
                list.add(null == largeClaimDetailsDto || null == largeClaimDetailsDto.getFac() ? "N/A" : largeClaimDetailsDto.getFac() + "%");//FAC/COINS%
                list.add(null == largeClaimDetailsDto || null == largeClaimDetailsDto.getRetention() ? "N/A" : largeClaimDetailsDto.getRetention() + "%");//Retention%
                list.add(null == largeClaimDetailsDto || null == largeClaimDetailsDto.getQs() ? "N/A" : largeClaimDetailsDto.getQs() + "%");//QS%
                list.add(null == largeClaimDetailsDto || null == largeClaimDetailsDto.getFac() ? "N/A" : largeClaimDetailsDto.getFac() + "%");////FAC/COINS%
                list.add("N/A");//Aportionment

                email.setParameterEmail(list);

                for (LargeClaimEmailReceiversDto receiversDto : automatedMailReceivers) {
                    email.setToAddresses(receiversDto.getEmail());
                    email.setSubject(emailSubject1);
                    emailService.sendEmail(connection, email);
                    if (isSeperateMails) {
                        email.setSubject(emailSubject2);
                        emailService.sendEmail(connection, email);
                    }
                }

                LargeClaimEmailHistoryDto emailHistoryDto = new LargeClaimEmailHistoryDto();
                emailHistoryDto.setClaimNo(claimNo);
                emailHistoryDto.setAcr(value);
                emailHistoryDto.setApprovedUser(user.getUserId());
                emailHistoryDto.setApprovedDate(Utility.sysDateTime());
                emailHistoryDto.setTotalPayable(BigDecimal.ZERO);
                emailHistoryDto.setCalsheetType("ACR Update");
                largeClaimEmailDao.saveHistory(connection, emailHistoryDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String callOnMiSiteOnlineAssessment(OnMiSiteReqDto dto, UserDto user) throws Exception {
        return ADMIN_ENDPOINT_URL.concat(saveOnlineAssessmentDetails(dto, user));
    }

    private String saveOnlineAssessmentDetails(OnMiSiteReqDto dto, UserDto user) throws Exception {

        try {
            dto.setAssignee(user.getUserId());
            String requestBody = objectMapper.writeValueAsString(dto);
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(SAVE_ENDPOINT_URL))
                    .header(CONTENT_TYPE, SAVE_ENDPOINT_CONTENT_TYPE)
                    .header(AUTHORIZATION, BEARER + this.getAccessToken())
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            String responseBody = response.body();

//        //todo - Solution for SSL handshake issue
//        URL url = new URL(SAVE_ENDPOINT_URL);
//        HttpsURLConnection con = (HttpsURLConnection) url.openConnection();
//
//        SSLContext sslContext = SSLContext.getInstance("TLS");
//        sslContext.init(null, new TrustManager[]{new X509TrustManager() {
//            public void checkClientTrusted(X509Certificate[] chain, String authType) {
//            }
//
//            public void checkServerTrusted(X509Certificate[] chain, String authType) {
//            }
//
//            public X509Certificate[] getAcceptedIssuers() {
//                return new X509Certificate[0];
//            }
//        }}, new SecureRandom());
//        con.setSSLSocketFactory(sslContext.getSocketFactory());
//
//        con.setHostnameVerifier((hostname, session) -> true);
//
//        con.setRequestMethod(HTTP_POST);
//        con.setRequestProperty(CONTENT_TYPE, SAVE_ENDPOINT_CONTENT_TYPE);
//        con.setRequestProperty(AUTHORIZATION, BEARER + this.getAccessToken());
//        con.setDoOutput(true);
//
//        try (OutputStream os = con.getOutputStream()) {
//            byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
//            os.write(input, 0, input.length);
//        }
//
//        int status = con.getResponseCode();
//        InputStream is = (status < 400) ? con.getInputStream() : con.getErrorStream();
//        String responseBody;
//        try (BufferedReader br = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
//            responseBody = br.lines().collect(Collectors.joining(System.lineSeparator()));
//        }

            Matcher matcher = Pattern.compile("\"accessToken\"\\s*:\\s*\"([^\"]+)\"").matcher(responseBody);

            if (matcher.find()) {
                return matcher.group(1);
            } else {
                throw new MisynRecordNotFoundException("Access token not found in save online assessment response: " + responseBody);
            }
        }catch (Exception e){
            throw e;
        }
    }

    /**
     * Generate token from the OnMiSite keycloak client
     */
    private String getAccessToken() throws Exception {
        String form = CLIENT_ID + TOKEN_CLIENT_ID + CLIENT_SECRET + TOKEN_CLIENT_SECRET + GRANT_TYPE + TOKEN_GRANT_TYPE + USERNAME + TOKEN_USERNAME + PASSWORD + TOKEN_PASSWORD;

        HttpRequest request = HttpRequest.newBuilder().uri(URI.create(TOKEN_URL)).header(CONTENT_TYPE, TOKEN_CONTENT_TYPE).POST(HttpRequest.BodyPublishers.ofString(form)).build();

        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        String responseBody = response.body();
        Matcher matcher = accessTokenExtractionRegex(responseBody);

        if (matcher.find()) {
            return matcher.group(1);
        } else {
            throw new MisynRecordNotFoundException("Access token not found in response: " + responseBody);
        }
    }
}
