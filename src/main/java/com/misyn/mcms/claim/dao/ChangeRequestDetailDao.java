package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ChangeRequestDetailDto;

import java.sql.Connection;
import java.util.List;

public interface ChangeRequestDetailDao {

    String INSERT_INTO_CHANGE_REQUEST_DETAIL_VALUE = "INSERT INTO change_request_detail VALUES (0,?,?,?,?,?,?,?,?)";

    String SELECT_ALL_FROM_CHANGE_REQUEST_DETAIL_BY_REF_NO = "SELECT * FROM change_request_detail WHERE ref_no = ?";

    String SELECT_ALL_FROM_CHANGE_REQUEST_DETAIL_BY_CLAIM_NO = "SELECT\n" +
            "	* \n" +
            "FROM\n" +
            "	change_request_detail \n" +
            "WHERE\n" +
            "	claim_no = ? \n" +
            "	AND request_user = ? \n" +
            "	AND response_type = 'P'";

    String UPDATE_CHANGE_REQUEST_DETAIL_BY_REF_NO = "UPDATE `change_request_detail` \n" +
            "SET `assign_rte` = ?,\n" +
            "`request_user` = ?,\n" +
            "`request_datetime` = ?,\n" +
            "`response_user` = ?,\n" +
            "`response_datetime` = ?,\n" +
            "`response_type` = ?\n" +
            "WHERE\n" +
            "	`ref_no` = ?";

    String SELECT_ONE_BY_REF_ID_AND_RESPONSE_TYPE = "SELECT 1 FROM change_request_detail WHERE ref_no = ? AND response_type = 'P'";

    ChangeRequestDetailDto insertMaster(Connection connection, ChangeRequestDetailDto changeRequestDetailDto) throws Exception;

    ChangeRequestDetailDto searchByRefNo(Connection connection, Integer refNo);

    void update(Connection connection, ChangeRequestDetailDto changeRequestDetailDto) throws Exception;

    List<ChangeRequestDetailDto> getAllrequestByclaimNoAndRequestUser(Connection connection, Integer claimNo, String alreadyAssignUser);

    boolean isChangeRequested(Connection connection, int refNo);

}
