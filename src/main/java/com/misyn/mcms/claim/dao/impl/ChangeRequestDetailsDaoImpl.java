package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.ChangeRequestDetailDao;
import com.misyn.mcms.claim.dto.ChangeRequestDetailDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class ChangeRequestDetailsDaoImpl implements ChangeRequestDetailDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChangeRequestDetailsDaoImpl.class);

    @Override
    public ChangeRequestDetailDto insertMaster(Connection connection, ChangeRequestDetailDto changeRequestDetailDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_INTO_CHANGE_REQUEST_DETAIL_VALUE);
            ps.setInt(++index, changeRequestDetailDto.getRefNo());
            ps.setInt(++index, changeRequestDetailDto.getClaimNo());
            ps.setString(++index, changeRequestDetailDto.getAssignRte());
            ps.setString(++index, changeRequestDetailDto.getRequestUser());
            ps.setString(++index, changeRequestDetailDto.getRequestDatetime());
            ps.setString(++index, changeRequestDetailDto.getResponseUser());
            ps.setString(++index, changeRequestDetailDto.getResponseDatetime());
            ps.setString(++index, changeRequestDetailDto.getResponseType());

            if (ps.executeUpdate() > 0) {
                return changeRequestDetailDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ChangeRequestDetailDto searchByRefNo(Connection connection, Integer refNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ChangeRequestDetailDto dto;
        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_CHANGE_REQUEST_DETAIL_BY_REF_NO);
            ps.setInt(1, refNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                return getChangeRequestDetailDto(rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public void update(Connection connection, ChangeRequestDetailDto changeRequestDetailDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CHANGE_REQUEST_DETAIL_BY_REF_NO);
            ps.setString(++index, changeRequestDetailDto.getAssignRte());
            ps.setString(++index, changeRequestDetailDto.getRequestUser());
            ps.setString(++index, changeRequestDetailDto.getRequestDatetime());
            ps.setString(++index, changeRequestDetailDto.getResponseUser());
            ps.setString(++index, changeRequestDetailDto.getResponseDatetime());
            ps.setString(++index, changeRequestDetailDto.getResponseType());
            ps.setInt(++index, changeRequestDetailDto.getRefNo());
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public List<ChangeRequestDetailDto> getAllrequestByclaimNoAndRequestUser(Connection connection, Integer claimNo, String alreadyAssignUser) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ChangeRequestDetailDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_CHANGE_REQUEST_DETAIL_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            ps.setString(2, alreadyAssignUser);
            rs = ps.executeQuery();
            while (rs.next()) {
                list.add(getChangeRequestDetailDto(rs));
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public boolean isChangeRequested(Connection connection, int refNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ChangeRequestDetailDto dto;
        try {
            ps = connection.prepareStatement(SELECT_ONE_BY_REF_ID_AND_RESPONSE_TYPE);
            ps.setInt(1, refNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return false;
    }

    private ChangeRequestDetailDto getChangeRequestDetailDto(ResultSet rs) {
        ChangeRequestDetailDto changeRequestDetailDto = new ChangeRequestDetailDto();

        try {
            changeRequestDetailDto.setTxnId(rs.getInt("txn_id"));
            changeRequestDetailDto.setRefNo(rs.getInt("ref_no"));
            changeRequestDetailDto.setClaimNo(rs.getInt("claim_no"));
            changeRequestDetailDto.setAssignRte(rs.getString("assign_rte"));
            changeRequestDetailDto.setRequestUser(rs.getString("request_user"));
            changeRequestDetailDto.setRequestDatetime(rs.getString("request_datetime"));
            changeRequestDetailDto.setResponseUser(rs.getString("response_user"));
            changeRequestDetailDto.setResponseDatetime(rs.getString("response_datetime"));
            changeRequestDetailDto.setResponseType(rs.getString("response_type"));
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        }


        return changeRequestDetailDto;
    }
}
