package com.misyn.mcms.admin.controller;

import com.google.gson.Gson;
import com.misyn.mcms.admin.admin.dto.UserGroupDto;
import com.misyn.mcms.admin.admin.dto.UserRoleDetailsDto;
import com.misyn.mcms.admin.admin.dto.UserRoleMstDto;
import com.misyn.mcms.admin.admin.service.RoleManagementService;
import com.misyn.mcms.admin.admin.service.impl.RoleManagementServiceImpl;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.utility.AppConstant;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

/**
 * Controller for role management functionality
 * Handles role assignment and group management operations
 */
@WebServlet(name = "RoleController", urlPatterns = "/RoleController/*")
public class RoleController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(RoleController.class);
    private final RoleManagementService roleManagementService = new RoleManagementServiceImpl();

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();
        UserDto user = getSessionUser(request);
        
        try {
            switch (pathInfo) {
                case "/loadRolesById":
                    loadRolesById(request, response);
                    break;
                case "/groupList":
                    getGroupList(request, response, user);
                    break;
                case "/saveGroup":
                    saveGroup(request, response, user);
                    break;
                case "/updateGroupRoles":
                    updateGroupRoles(request, response, user);
                    break;
                case "/deleteGroup":
                    deleteGroup(request, response);
                    break;
                case "/enableGroup":
                    enableGroup(request, response);
                    break;
                case "/disableGroup":
                    disableGroup(request, response);
                    break;
                case "/roleManagementView":
                    roleManagementView(request, response);
                    break;
                default:
                    response.sendError(HttpServletResponse.SC_NOT_FOUND);
                    break;
            }
        } catch (Exception e) {
            LOGGER.error("Error processing role management request: {}", e.getMessage(), e);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            } catch (IOException ioException) {
                LOGGER.error("Error sending error response: {}", ioException.getMessage(), ioException);
            }
        }
    }

    /**
     * Load roles by group ID and return separated available and assigned roles
     * This eliminates the need for frontend nested loops
     */
    private void loadRolesById(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String groupIdParam = request.getParameter("groupId");
        if (groupIdParam == null || groupIdParam.trim().isEmpty()) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Group ID is required");
            return;
        }

        try {
            int groupId = Integer.parseInt(groupIdParam);
            UserRoleDetailsDto roleDetails = roleManagementService.getRolesByGroupId(groupId);
            
            // Set both lists as request attributes for JSP access
            request.setAttribute("availableRoles", roleDetails.getAvailableRoles());
            request.setAttribute("assignedRoles", roleDetails.getAssignedRoles());
            request.setAttribute("roleDetails", roleDetails);
            
            // For AJAX requests, return JSON
            if (isAjaxRequest(request)) {
                Gson gson = new Gson();
                String json = gson.toJson(roleDetails);
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                response.getWriter().write(json);
            } else {
                // Forward to JSP for regular requests
                requestDispatcher(request, response, "/admin/role_management/roleAssignment.jsp");
            }
        } catch (NumberFormatException e) {
            LOGGER.error("Invalid group ID format: {}", groupIdParam);
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid group ID format");
        }
    }

    private void getGroupList(HttpServletRequest request, HttpServletResponse response, UserDto user) throws Exception {
        List<UserGroupDto> groups = roleManagementService.getGroupList(user.getUserId());
        
        if (isAjaxRequest(request)) {
            Gson gson = new Gson();
            String json = gson.toJson(groups);
            printWriter(request, response, json);
        } else {
            request.setAttribute("groupList", groups);
            requestDispatcher(request, response, "/admin/role_management/groupList.jsp");
        }
    }

    private void saveGroup(HttpServletRequest request, HttpServletResponse response, UserDto user) throws Exception {
        String groupName = request.getParameter("groupName");
        if (groupName == null || groupName.trim().isEmpty()) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Group name is required");
            return;
        }

        UserGroupDto userGroupDto = new UserGroupDto();
        userGroupDto.setGroupName(groupName.trim());
        userGroupDto.setCreatedBy(user.getUserId());
        userGroupDto.setStatus("ACTIVE");

        try {
            roleManagementService.saveUserGroup(userGroupDto);
            
            Gson gson = new Gson();
            String json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error("Error saving group: {}", e.getMessage(), e);
            Gson gson = new Gson();
            String json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void updateGroupRoles(HttpServletRequest request, HttpServletResponse response, UserDto user) throws Exception {
        String groupIdParam = request.getParameter("groupId");
        String roleIdsParam = request.getParameter("roleIds");
        
        if (groupIdParam == null || groupIdParam.trim().isEmpty()) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Group ID is required");
            return;
        }

        try {
            int groupId = Integer.parseInt(groupIdParam);
            List<Integer> roleIds = parseRoleIds(roleIdsParam);
            
            roleManagementService.updateGroupRoles(groupId, roleIds, user.getUserId());
            
            Gson gson = new Gson();
            String json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (NumberFormatException e) {
            LOGGER.error("Invalid group ID format: {}", groupIdParam);
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid group ID format");
        } catch (Exception e) {
            LOGGER.error("Error updating group roles: {}", e.getMessage(), e);
            Gson gson = new Gson();
            String json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void deleteGroup(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String groupIdParam = request.getParameter("groupId");
        if (groupIdParam == null || groupIdParam.trim().isEmpty()) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Group ID is required");
            return;
        }

        try {
            int groupId = Integer.parseInt(groupIdParam);
            roleManagementService.deleteGroup(groupId);
            
            Gson gson = new Gson();
            String json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (NumberFormatException e) {
            LOGGER.error("Invalid group ID format: {}", groupIdParam);
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid group ID format");
        } catch (Exception e) {
            LOGGER.error("Error deleting group: {}", e.getMessage(), e);
            Gson gson = new Gson();
            String json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void enableGroup(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String groupIdParam = request.getParameter("groupId");
        if (groupIdParam == null || groupIdParam.trim().isEmpty()) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Group ID is required");
            return;
        }

        try {
            int groupId = Integer.parseInt(groupIdParam);
            roleManagementService.enableGroup(groupId);
            
            Gson gson = new Gson();
            String json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (NumberFormatException e) {
            LOGGER.error("Invalid group ID format: {}", groupIdParam);
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid group ID format");
        } catch (Exception e) {
            LOGGER.error("Error enabling group: {}", e.getMessage(), e);
            Gson gson = new Gson();
            String json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void disableGroup(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String groupIdParam = request.getParameter("groupId");
        if (groupIdParam == null || groupIdParam.trim().isEmpty()) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Group ID is required");
            return;
        }

        try {
            int groupId = Integer.parseInt(groupIdParam);
            roleManagementService.disableGroup(groupId);
            
            Gson gson = new Gson();
            String json = gson.toJson("SUCCESS");
            printWriter(request, response, json);
        } catch (NumberFormatException e) {
            LOGGER.error("Invalid group ID format: {}", groupIdParam);
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid group ID format");
        } catch (Exception e) {
            LOGGER.error("Error disabling group: {}", e.getMessage(), e);
            Gson gson = new Gson();
            String json = gson.toJson("FAIL");
            printWriter(request, response, json);
        }
    }

    private void roleManagementView(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // Load all roles for the role management interface
        List<UserRoleMstDto> allRoles = roleManagementService.getAllRoles();
        request.setAttribute("allRoles", allRoles);
        requestDispatcher(request, response, "/admin/role_management/roleManagement.jsp");
    }

    /**
     * Parse comma-separated role IDs string into List<Integer>
     */
    private List<Integer> parseRoleIds(String roleIdsParam) {
        if (roleIdsParam == null || roleIdsParam.trim().isEmpty()) {
            return List.of();
        }
        
        return List.of(roleIdsParam.split(","))
                .stream()
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Integer::parseInt)
                .toList();
    }

    /**
     * Check if the request is an AJAX request
     */
    private boolean isAjaxRequest(HttpServletRequest request) {
        String requestedWith = request.getHeader("X-Requested-With");
        return "XMLHttpRequest".equals(requestedWith);
    }
}
