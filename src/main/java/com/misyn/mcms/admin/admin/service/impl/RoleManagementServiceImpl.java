package com.misyn.mcms.admin.admin.service.impl;

import com.misyn.mcms.admin.admin.dao.UserGroupDao;
import com.misyn.mcms.admin.admin.dao.UserRoleDetailsDao;
import com.misyn.mcms.admin.admin.dao.UserRoleMstDao;
import com.misyn.mcms.admin.admin.dao.impl.UserGroupDaoImpl;
import com.misyn.mcms.admin.admin.dao.impl.UserRoleDetailsDaoImpl;
import com.misyn.mcms.admin.admin.dao.impl.UserRoleMstDaoImpl;
import com.misyn.mcms.admin.admin.dto.UserGroupDto;
import com.misyn.mcms.admin.admin.dto.UserRoleDetailsDto;
import com.misyn.mcms.admin.admin.dto.UserRoleMstDto;
import com.misyn.mcms.admin.admin.service.RoleManagementService;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakGroupDto;
import com.misyn.mcms.claim.exception.KeycloakCustomException;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.keycloak.KeycloakUserService;
import com.misyn.mcms.claim.service.keycloak.impl.KeycloakUserServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class RoleManagementServiceImpl extends AbstractBaseService<RoleManagementServiceImpl> implements RoleManagementService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RoleManagementServiceImpl.class);
    private final UserGroupDao userGroupDao = new UserGroupDaoImpl();
    private final UserRoleMstDao userRoleMstDao = new UserRoleMstDaoImpl();
    private final UserRoleDetailsDao userRoleDetailsDao = new UserRoleDetailsDaoImpl();
    private final KeycloakUserService keycloakUserService = new KeycloakUserServiceImpl();

    @Override
    public List<UserGroupDto> getGroupList(String userName) throws Exception {
        Connection connection = null;
        List<KeyCloakGroupDto> allGroups;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);

            try {
                allGroups = keycloakUserService.getAllGroups(userName).stream().filter(
                        Group ->
                                Group.getName() != null && Group.getName()
                                        .startsWith(AppConstant.GROUP_PREFIX)).toList();
            } catch (RuntimeException e) {
                LOGGER.error(e.getMessage(), e);
                throw new KeycloakCustomException("Cannot fetch user roles from keycloak.");
            }

            List<UserGroupDto> userGroupDtos = userGroupDao.searchAll(connection);
            List<KeyCloakGroupDto> finalAllGroups = allGroups;

            // Synchronize Keycloak groups with local database
            synchronizeGroupsWithKeycloak(connection, finalAllGroups, userGroupDtos, userName);

            commitTransaction(connection);

            // Return updated list from database
            return userGroupDao.searchAll(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public UserGroupDto saveUserGroup(UserGroupDto userGroupDto) throws Exception {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            chekUserGroupValidationOnCreate(userGroupDto, connection);
            KeyCloakGroupDto keyCloakGroupDto = new KeyCloakGroupDto();
            keyCloakGroupDto.setName(
                    userGroupDto.getGroupName()
            );
            try {
                keycloakUserService.createGroup(
                        keyCloakGroupDto,
                        userGroupDto.getCreatedBy()
                );
            } catch (RuntimeException e) {
                LOGGER.error(e.getMessage(), e);
                throw new KeycloakCustomException("Cannot create user group in keycloak.");
            }
            userGroupDao.insertMaster(connection, userGroupDto);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error("Failed to save user: {}", e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return userGroupDto;
    }
    private void chekUserGroupValidationOnCreate(UserGroupDto userGroupDto, Connection connection) throws Exception {
        if (userGroupDao.isGroupNameExists(connection, userGroupDto.getGroupName())) {
            throw new MisynJDBCException("Group Name '" + userGroupDto.getGroupName() + "' already exists.");
        }
    }

    @Override
    public UserRoleDetailsDto getRolesByGroupId(int groupId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            UserGroupDto group = userGroupDao.searchMaster(connection, groupId);
            if (group == null) {
                throw new Exception("Group not found with ID: " + groupId);
            }

            // Get all roles from the system
            List<UserRoleMstDto> allRoles = userRoleMstDao.searchAll(connection);

            UserRoleDetailsDto roleDetails = userRoleDetailsDao.getRoleDetailsByGroupId(connection, groupId);
            if (roleDetails == null) {
                roleDetails = new UserRoleDetailsDto();
                roleDetails.setGroupId(groupId);
                roleDetails.setRoleIds("");
            }
            roleDetails.setGroupName(group.getGroupName());

            // Parse assigned role IDs
            List<Integer> assignedRoleIds = new ArrayList<>();
            if (roleDetails.getRoleIds() != null && !roleDetails.getRoleIds().trim().isEmpty()) {
                String[] roleIdArray = roleDetails.getRoleIds().split(",");
                for (String roleId : roleIdArray) {
                    try {
                        assignedRoleIds.add(Integer.parseInt(roleId.trim()));
                    } catch (NumberFormatException e) {
                        LOGGER.warn("Invalid role ID: " + roleId);
                    }
                }
            }

            // Separate roles into assigned and available lists
            List<UserRoleMstDto> assignedRoles = new ArrayList<>();
            List<UserRoleMstDto> availableRoles = new ArrayList<>();

            for (UserRoleMstDto role : allRoles) {
                if (assignedRoleIds.contains(role.getRoleId())) {
                    assignedRoles.add(role);
                } else {
                    availableRoles.add(role);
                }
            }

            roleDetails.setAssignedRoles(assignedRoles);
            roleDetails.setAvailableRoles(availableRoles);

            return roleDetails;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updateGroupRoles(int groupId, List<Integer> roleIds, String updatedBy) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);

            String roleIdsString = roleIds != null && !roleIds.isEmpty() ? roleIds.stream().map(String::valueOf).collect(Collectors.joining(",")) : "";
            UserRoleDetailsDto existingDetails = userRoleDetailsDao.getRoleDetailsByGroupId(connection, groupId);
            if (existingDetails != null) {
                userRoleDetailsDao.updateRolesByGroupId(connection, groupId, roleIdsString, updatedBy);
            } else {
                userRoleDetailsDao.insertRoleDetails(connection, groupId, userGroupDao.searchMaster(connection, groupId).getGroupName(), roleIdsString, updatedBy);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void deleteGroup(int groupId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            UserGroupDto group = userGroupDao.searchMaster(connection, groupId);
            if (group != null) {
                group.setStatus("DELETED");
                userGroupDao.updateMaster(connection, group);
                userRoleDetailsDao.deleteRoleDetailsByGroupId(connection, groupId);
            }

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void disableGroup(int groupId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            UserGroupDto group = userGroupDao.searchMaster(connection, groupId);
            if (group != null) {
                group.setStatus("DISABLED");
                userGroupDao.updateMaster(connection, group);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<UserRoleMstDto> getAllRoles() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return userRoleMstDao.searchAll(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void enableGroup(int groupId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            UserGroupDto group = userGroupDao.searchMaster(connection, groupId);
            if (group != null) {
                group.setStatus("ACTIVE");
                userGroupDao.updateMaster(connection, group);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public UserGroupDto getGroupById(int groupId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return userGroupDao.getGroupById(connection, groupId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    /**
     * Synchronizes Keycloak groups with local database
     * - Adds new groups from Keycloak as ACTIVE
     * - Marks groups missing from Keycloak as DELETED
     */
    private void synchronizeGroupsWithKeycloak(Connection connection, List<KeyCloakGroupDto> keycloakGroups,
                                             List<UserGroupDto> localGroups, String userName) throws Exception {

        // Create sets for efficient lookup
        Set<String> keycloakGroupNames = keycloakGroups.stream()
                .map(KeyCloakGroupDto::getName)
                .collect(Collectors.toSet());

        Set<String> localGroupNames = localGroups.stream()
                .map(UserGroupDto::getGroupName)
                .collect(Collectors.toSet());

        // Find groups in Keycloak but missing from local database
        for (KeyCloakGroupDto keycloakGroup : keycloakGroups) {
            if (!localGroupNames.contains(keycloakGroup.getName())) {
                LOGGER.info("Adding new group from Keycloak: {}", keycloakGroup.getName());
                UserGroupDto newGroup = new UserGroupDto();
                newGroup.setGroupName(keycloakGroup.getName());
                newGroup.setStatus("ACTIVE");
                newGroup.setCreatedBy(userName);
                userGroupDao.insertMaster(connection, newGroup);
            }
        }

        // Find groups in local database but missing from Keycloak
        for (UserGroupDto localGroup : localGroups) {
            if (!keycloakGroupNames.contains(localGroup.getGroupName()) &&
                !"DELETED".equals(localGroup.getStatus())) {
                LOGGER.info("Marking group as DELETED (missing from Keycloak): {}", localGroup.getGroupName());
                localGroup.setStatus("DELETED");
                userGroupDao.updateMaster(connection, localGroup);
            }
        }
    }

}
