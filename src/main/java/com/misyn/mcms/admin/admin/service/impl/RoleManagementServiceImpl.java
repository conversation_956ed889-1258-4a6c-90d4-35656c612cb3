package com.misyn.mcms.admin.admin.service.impl;

import com.misyn.mcms.admin.admin.dao.UserGroupDao;
import com.misyn.mcms.admin.admin.dao.UserRoleDetailsDao;
import com.misyn.mcms.admin.admin.dao.UserRoleMstDao;
import com.misyn.mcms.admin.admin.dao.impl.UserGroupDaoImpl;
import com.misyn.mcms.admin.admin.dao.impl.UserRoleDetailsDaoImpl;
import com.misyn.mcms.admin.admin.dao.impl.UserRoleMstDaoImpl;
import com.misyn.mcms.admin.admin.dto.UserGroupDto;
import com.misyn.mcms.admin.admin.dto.UserRoleDetailsDto;
import com.misyn.mcms.admin.admin.dto.UserRoleMstDto;
import com.misyn.mcms.admin.admin.service.RoleManagementService;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakGroupDto;
import com.misyn.mcms.claim.exception.KeycloakCustomException;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.keycloak.KeycloakUserService;
import com.misyn.mcms.claim.service.keycloak.impl.KeycloakUserServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class RoleManagementServiceImpl extends AbstractBaseService<RoleManagementServiceImpl> implements RoleManagementService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RoleManagementServiceImpl.class);
    private final UserGroupDao userGroupDao = new UserGroupDaoImpl();
    private final UserRoleMstDao userRoleMstDao = new UserRoleMstDaoImpl();
    private final UserRoleDetailsDao userRoleDetailsDao = new UserRoleDetailsDaoImpl();
    private final KeycloakUserService keycloakUserService = new KeycloakUserServiceImpl();

    @Override
    public List<UserGroupDto> getGroupList(String userName) throws Exception {
        Connection connection = null;
        List<KeyCloakGroupDto> allGroups;
        try {
            connection = getJDBCConnection();
            try {
                allGroups = keycloakUserService.getAllGroups(userName).stream().filter(
                        Group ->
                                Group.getName() != null && Group.getName()
                                        .startsWith(AppConstant.GROUP_PREFIX)).toList();
            } catch (RuntimeException e) {
                LOGGER.error(e.getMessage(), e);
                throw new KeycloakCustomException("Cannot fetch user roles from keycloak.");
            }
            List<UserGroupDto> userGroupList= userGroupDao.searchAll(connection);
            List<KeyCloakGroupDto> finalAllGroups = allGroups;
            if (!userGroupList.stream().allMatch(
                    groupDto -> finalAllGroups.stream().anyMatch(
                            group -> group.getName().equals(groupDto.getGroupName())))) {
                throw new KeycloakCustomException("One or more groups not available in Group identity provider or MCMS user groups.");


            }
            return userGroupList;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public UserGroupDto saveUserGroup(UserGroupDto userGroupDto) throws Exception {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            chekUserGroupValidationOnCreate(userGroupDto, connection);
            KeyCloakGroupDto keyCloakGroupDto = new KeyCloakGroupDto();
            keyCloakGroupDto.setName(
                    userGroupDto.getGroupName()
            );
            try {
                keycloakUserService.createGroup(
                        keyCloakGroupDto,
                        userGroupDto.getCreatedBy()
                );
            } catch (RuntimeException e) {
                LOGGER.error(e.getMessage(), e);
                throw new KeycloakCustomException("Cannot create user group in keycloak.");
            }
            userGroupDao.insertMaster(connection, userGroupDto);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error("Failed to save user: {}", e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return userGroupDto;
    }
    private void chekUserGroupValidationOnCreate(UserGroupDto userGroupDto, Connection connection) throws Exception {
        if (userGroupDao.isGroupNameExists(connection, userGroupDto.getGroupName())) {
            throw new MisynJDBCException("Group Name '" + userGroupDto.getGroupName() + "' already exists.");
        }
    }

    @Override
    public UserRoleDetailsDto getRolesByGroupId(int groupId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            UserGroupDto group = userGroupDao.searchMaster(connection, groupId);
            if (group == null) {
                throw new Exception("Group not found with ID: " + groupId);
            }
            UserRoleDetailsDto roleDetails = userRoleDetailsDao.getRoleDetailsByGroupId(connection, groupId);
            if (roleDetails == null) {
                roleDetails = new UserRoleDetailsDto();
                roleDetails.setGroupId(groupId);
                roleDetails.setRoleIds("");
            }
            roleDetails.setGroupName(group.getGroupName());
            if (roleDetails.getRoleIds() != null && !roleDetails.getRoleIds().trim().isEmpty()) {
                String[] roleIdArray = roleDetails.getRoleIds().split(",");
                List<Integer> roleIds = new ArrayList<>();
                for (String roleId : roleIdArray) {
                    try {
                        roleIds.add(Integer.parseInt(roleId.trim()));
                    } catch (NumberFormatException e) {
                        LOGGER.warn("Invalid role ID: " + roleId);
                    }
                }
                if (!roleIds.isEmpty()) {
                    List<UserRoleMstDto> assignedRoles = userRoleMstDao.getRolesByIds(connection, roleIds);
                    roleDetails.setAssignedRoles(assignedRoles);
                }
            }

            return roleDetails;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updateGroupRoles(int groupId, List<Integer> roleIds, String updatedBy) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);

            String roleIdsString = roleIds != null && !roleIds.isEmpty() ? roleIds.stream().map(String::valueOf).collect(Collectors.joining(",")) : "";
            UserRoleDetailsDto existingDetails = userRoleDetailsDao.getRoleDetailsByGroupId(connection, groupId);
            if (existingDetails != null) {
                userRoleDetailsDao.updateRolesByGroupId(connection, groupId, roleIdsString, updatedBy);
            } else {
                userRoleDetailsDao.insertRoleDetails(connection, groupId, userGroupDao.searchMaster(connection, groupId).getGroupName(), roleIdsString, updatedBy);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void deleteGroup(int groupId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            UserGroupDto group = userGroupDao.searchMaster(connection, groupId);
            if (group != null) {
                group.setStatus("DELETED");
                userGroupDao.updateMaster(connection, group);
                userRoleDetailsDao.deleteRoleDetailsByGroupId(connection, groupId);
            }

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void disableGroup(int groupId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            UserGroupDto group = userGroupDao.searchMaster(connection, groupId);
            if (group != null) {
                group.setStatus("DISABLED");
                userGroupDao.updateMaster(connection, group);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<UserRoleMstDto> getAllRoles() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return userRoleMstDao.searchAll(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void enableGroup(int groupId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            UserGroupDto group = userGroupDao.searchMaster(connection, groupId);
            if (group != null) {
                group.setStatus("ACTIVE");
                userGroupDao.updateMaster(connection, group);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public UserGroupDto getGroupById(int groupId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return userGroupDao.getGroupById(connection, groupId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

}
