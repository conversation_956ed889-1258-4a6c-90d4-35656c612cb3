<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.Date" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <title>Sample JSP Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .table th, .table td {
            vertical-align: middle;
        }
    </style>
    <link href="/css/common/tableStyle.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div class="container-fluid">
    <div class="row header-bg">
        <div class="col-sm-12 py-2 bg-dark">
            <h5 class="float-left text-dark">System User Groups</h5>
        </div>
    </div>

    <div class="row">
    <div class="col-sm-12 py-2 text-right">
        <button class="btn btn-success btn-sm" data-toggle="modal" data-target="#addRoleModal">
            + User Group
        </button>
    </div>
    </div>
    <!-- Data Table -->
    <table id="user-privileges-table" class="table table-sm table-hover" cellspacing="0" style="cursor:pointer" width="100%">
        <thead class="blueheader">
        <tr>
            <th>Group Id</th>
            <th>Group Name</th>
            <th>Status</th>
            <th>Created Date</th>
            <th>Created By</th>
            <th>Status Updated Date</th>
            <th style="width: 10%">Action</th>
        </tr>
        </thead>
        <tbody id="roles-table-body">
        <c:forEach var="group" items="${groupList}">
            <tr>
                <td>${group.groupId}</td>
                <td>${group.groupName}</td>
                <c:if test="${group.status == 'ACTIVE'}">
                    <td><span class="badge-success">${group.status}</span></td>
                </c:if>
                <c:if test="${group.status == 'DISABLED'}">
                    <td><span class="badge-danger">${group.status}</span></td>
                </c:if>
                <td>${group.createdDate}</td>
                <td>${group.createdBy}</td>
                <td>${group.statusUpdatedDate}</td>
                <td>
                    <div style="display: flex;gap: 5px">
                        <button class='btn-primary btn btn-sm' style="height: 30px; width: 50px; align-items: center; margin: auto;" onclick="loadRole('${group.groupId}')"> <i class="fa fa-edit"></i></button>
                    </div>
                </td>
            </tr>
        </c:forEach>

        </tbody>
    </table>
</div>

<!-- Modal -->
<div class="modal fade" id="addRoleModal" tabindex="-1" role="dialog" aria-labelledby="addRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRoleModalLabel">Add New User Group</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="roleForm">
                    <div class="form-group">
                        <label for="roleName">Group Name</label>
                        <input type="text" class="form-control" id="groupName" placeholder="Enter group name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">Close</button>
                <button type="button" id="saveUserGroupBtn" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>
    </div>
</div>
</body>
<script>
    // Set Validations
    const input = document.getElementById("groupName");
    input.value = "MCMS_";
    function capitalizeAfterUnderscore(str) {
        return str
            .split("_")
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join("_");
    }
    input.addEventListener("input", function () {
        let value = input.value;

        if (!value.startsWith("MCMS_")) {
            value = "MCMS_" + value.replace(/^MCMS_/i, "");
        }
        let afterPrefix = value.slice(5);
        afterPrefix = afterPrefix.replace(/\s+/g, "_");
        afterPrefix = capitalizeAfterUnderscore(afterPrefix);
        input.value = "MCMS_" + afterPrefix;
        input.selectionStart = input.selectionEnd = input.value.length;
    });

//save user group
    $('#saveUserGroupBtn').click(function() {
        const groupName = $('#groupName').val();
        if (groupName.trim() === "") {
            notify("Please enter a group name.", "danger");
            return;
        }
        $.ajax({
            url: contextPath + "/rolesController/saveUserGroup",
            type: 'POST',
            contentType: 'application/json; charset=utf-8',
            data: JSON.stringify({
                groupName: groupName
            }),
            success: function (result) {
                try {
                    var messageType = result;
                    if (messageType.status == "success") {
                        $('#addRoleModal').modal('hide');
                        notify("User Group created successfully!", "success");
                        setTimeout(function() {
                            window.location = contextPath + "/rolesController/loadAllGroups";
                        }, 2500);
                    } else {
                        notify("Failed to create user Group: " + messageType.message , "danger");
                    }
                } catch (e) {
                    console.error("Error parsing server response:", e);
                    alert("An unexpected error occurred.");
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error("AJAX Error:", textStatus, errorThrown);
                notify("A server error occurred. Failed to save the user group.", "danger");
            }
        });
    });

    function loadRole(groupId) {
        window.location = contextPath + "/rolesController/loadRoleById?groupId=" + groupId;
    }
    hideLoader()
</script>
</html>
