<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Role Assignment</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/bootstrap.min.css">
    <style>
        .transfer-container {
            display: flex;
            gap: 20px;
            align-items: center;
            margin: 20px 0;
        }
        .transfer-box {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            width: 300px;
            height: 400px;
            overflow-y: auto;
        }
        .transfer-item {
            padding: 8px;
            margin: 2px 0;
            border: 1px solid #eee;
            border-radius: 3px;
            cursor: pointer;
        }
        .transfer-item:hover {
            background-color: #f5f5f5;
        }
        .transfer-item.selected {
            background-color: #007bff;
            color: white;
        }
        .transfer-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .btn-transfer {
            width: 80px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h3>Role Assignment for Group: ${roleDetails.groupName}</h3>
                
                <div class="transfer-container">
                    <!-- Available Roles -->
                    <div>
                        <h5>Available Roles</h5>
                        <div id="availableRoles" class="transfer-box">
                            <c:forEach var="role" items="${availableRoles}">
                                <div class="transfer-item" data-role-id="${role.roleId}" onclick="selectItem(this)">
                                    <input type="checkbox" value="${role.roleId}" style="margin-right: 8px;">
                                    ${role.roleName}
                                </div>
                            </c:forEach>
                        </div>
                    </div>
                    
                    <!-- Transfer Controls -->
                    <div class="transfer-controls">
                        <button type="button" class="btn btn-primary btn-transfer" onclick="moveSelected('available', 'assigned')">
                            Add &gt;&gt;
                        </button>
                        <button type="button" class="btn btn-primary btn-transfer" onclick="moveSelected('assigned', 'available')">
                            &lt;&lt; Remove
                        </button>
                        <button type="button" class="btn btn-success btn-transfer" onclick="moveAll('available', 'assigned')">
                            Add All &gt;&gt;
                        </button>
                        <button type="button" class="btn btn-warning btn-transfer" onclick="moveAll('assigned', 'available')">
                            &lt;&lt; Remove All
                        </button>
                    </div>
                    
                    <!-- Assigned Roles -->
                    <div>
                        <h5>Assigned Roles</h5>
                        <div id="assignedRoles" class="transfer-box">
                            <c:forEach var="role" items="${assignedRoles}">
                                <div class="transfer-item" data-role-id="${role.roleId}" onclick="selectItem(this)">
                                    <input type="checkbox" value="${role.roleId}" style="margin-right: 8px;">
                                    ${role.roleName}
                                </div>
                            </c:forEach>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="mt-3">
                    <button type="button" class="btn btn-success" onclick="saveRoleAssignment()">Save Changes</button>
                    <button type="button" class="btn btn-secondary" onclick="cancelChanges()">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script src="${pageContext.request.contextPath}/js/jquery.min.js"></script>
    <script src="${pageContext.request.contextPath}/js/bootstrap.min.js"></script>
    <script>
        function selectItem(element) {
            const checkbox = element.querySelector('input[type="checkbox"]');
            checkbox.checked = !checkbox.checked;
            
            if (checkbox.checked) {
                element.classList.add('selected');
            } else {
                element.classList.remove('selected');
            }
        }

        function moveSelected(fromType, toType) {
            const fromContainer = document.getElementById(fromType + 'Roles');
            const toContainer = document.getElementById(toType + 'Roles');
            
            const selectedItems = fromContainer.querySelectorAll('.transfer-item.selected');
            
            selectedItems.forEach(item => {
                item.classList.remove('selected');
                const checkbox = item.querySelector('input[type="checkbox"]');
                checkbox.checked = false;
                toContainer.appendChild(item);
            });
        }

        function moveAll(fromType, toType) {
            const fromContainer = document.getElementById(fromType + 'Roles');
            const toContainer = document.getElementById(toType + 'Roles');
            
            const allItems = fromContainer.querySelectorAll('.transfer-item');
            
            allItems.forEach(item => {
                item.classList.remove('selected');
                const checkbox = item.querySelector('input[type="checkbox"]');
                checkbox.checked = false;
                toContainer.appendChild(item);
            });
        }

        function saveRoleAssignment() {
            const assignedContainer = document.getElementById('assignedRoles');
            const assignedRoleIds = Array.from(assignedContainer.querySelectorAll('.transfer-item'))
                .map(item => item.getAttribute('data-role-id'))
                .join(',');
            
            const groupId = ${roleDetails.groupId};
            
            $.ajax({
                url: '${pageContext.request.contextPath}/RoleController/updateGroupRoles',
                type: 'POST',
                data: {
                    groupId: groupId,
                    roleIds: assignedRoleIds
                },
                success: function(response) {
                    if (response === 'SUCCESS') {
                        alert('Role assignment updated successfully!');
                        // Optionally redirect or refresh
                        window.location.reload();
                    } else {
                        alert('Failed to update role assignment. Please try again.');
                    }
                },
                error: function() {
                    alert('An error occurred while updating role assignment.');
                }
            });
        }

        function cancelChanges() {
            if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                window.history.back();
            }
        }

        // Initialize: Make sure no items are selected on page load
        $(document).ready(function() {
            $('.transfer-item').removeClass('selected');
            $('.transfer-item input[type="checkbox"]').prop('checked', false);
        });
    </script>
</body>
</html>
