<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Role Management</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/bootstrap.min.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/datatables.min.css">
    <style>
        .group-actions {
            margin: 10px 0;
        }
        .status-active {
            color: #28a745;
            font-weight: bold;
        }
        .status-disabled {
            color: #ffc107;
            font-weight: bold;
        }
        .status-deleted {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h2>Role Management System</h2>
                
                <!-- Group Management Section -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h4>Group Management</h4>
                    </div>
                    <div class="card-body">
                        <div class="group-actions">
                            <button type="button" class="btn btn-primary" onclick="showCreateGroupModal()">
                                Create New Group
                            </button>
                            <button type="button" class="btn btn-info" onclick="refreshGroupList()">
                                Refresh Groups
                            </button>
                        </div>
                        
                        <table id="groupTable" class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>Group ID</th>
                                    <th>Group Name</th>
                                    <th>Status</th>
                                    <th>Created Date</th>
                                    <th>Created By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Group Modal -->
    <div class="modal fade" id="createGroupModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Group</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="createGroupForm">
                        <div class="form-group">
                            <label for="groupName">Group Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="groupName" name="groupName" required>
                            <small class="form-text text-muted">Enter a unique group name</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="createGroup()">Create Group</button>
                </div>
            </div>
        </div>
    </div>

    <script src="${pageContext.request.contextPath}/js/jquery.min.js"></script>
    <script src="${pageContext.request.contextPath}/js/bootstrap.min.js"></script>
    <script src="${pageContext.request.contextPath}/js/datatables.min.js"></script>
    <script>
        let groupTable;

        $(document).ready(function() {
            initializeGroupTable();
            loadGroupList();
        });

        function initializeGroupTable() {
            groupTable = $('#groupTable').DataTable({
                columns: [
                    { data: 'groupId' },
                    { data: 'groupName' },
                    { 
                        data: 'status',
                        render: function(data, type, row) {
                            let statusClass = 'status-active';
                            if (data === 'DISABLED') statusClass = 'status-disabled';
                            if (data === 'DELETED') statusClass = 'status-deleted';
                            return '<span class="' + statusClass + '">' + data + '</span>';
                        }
                    },
                    { data: 'createdDate' },
                    { data: 'createdBy' },
                    {
                        data: null,
                        render: function(data, type, row) {
                            let actions = '<div class="btn-group" role="group">';
                            
                            // Assign Roles button
                            actions += '<button type="button" class="btn btn-sm btn-info" onclick="assignRoles(' + row.groupId + ')" title="Assign Roles">';
                            actions += '<i class="fa fa-users"></i> Roles</button>';
                            
                            // Enable/Disable button
                            if (row.status === 'ACTIVE') {
                                actions += '<button type="button" class="btn btn-sm btn-warning" onclick="disableGroup(' + row.groupId + ')" title="Disable Group">';
                                actions += '<i class="fa fa-pause"></i> Disable</button>';
                            } else if (row.status === 'DISABLED') {
                                actions += '<button type="button" class="btn btn-sm btn-success" onclick="enableGroup(' + row.groupId + ')" title="Enable Group">';
                                actions += '<i class="fa fa-play"></i> Enable</button>';
                            }
                            
                            // Delete button (only for non-deleted groups)
                            if (row.status !== 'DELETED') {
                                actions += '<button type="button" class="btn btn-sm btn-danger" onclick="deleteGroup(' + row.groupId + ')" title="Delete Group">';
                                actions += '<i class="fa fa-trash"></i> Delete</button>';
                            }
                            
                            actions += '</div>';
                            return actions;
                        }
                    }
                ],
                order: [[0, 'asc']],
                pageLength: 25,
                responsive: true
            });
        }

        function loadGroupList() {
            $.ajax({
                url: '${pageContext.request.contextPath}/RoleController/groupList',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    groupTable.clear().rows.add(data).draw();
                },
                error: function() {
                    alert('Failed to load group list. Please refresh the page.');
                }
            });
        }

        function showCreateGroupModal() {
            $('#createGroupForm')[0].reset();
            $('#createGroupModal').modal('show');
        }

        function createGroup() {
            const groupName = $('#groupName').val().trim();
            
            if (!groupName) {
                alert('Please enter a group name.');
                return;
            }

            $.ajax({
                url: '${pageContext.request.contextPath}/RoleController/saveGroup',
                type: 'POST',
                data: { groupName: groupName },
                success: function(response) {
                    if (response === 'SUCCESS') {
                        $('#createGroupModal').modal('hide');
                        alert('Group created successfully!');
                        loadGroupList();
                    } else {
                        alert('Failed to create group. Please try again.');
                    }
                },
                error: function() {
                    alert('An error occurred while creating the group.');
                }
            });
        }

        function assignRoles(groupId) {
            window.location.href = '${pageContext.request.contextPath}/RoleController/loadRolesById?groupId=' + groupId;
        }

        function enableGroup(groupId) {
            if (confirm('Are you sure you want to enable this group?')) {
                $.ajax({
                    url: '${pageContext.request.contextPath}/RoleController/enableGroup',
                    type: 'POST',
                    data: { groupId: groupId },
                    success: function(response) {
                        if (response === 'SUCCESS') {
                            alert('Group enabled successfully!');
                            loadGroupList();
                        } else {
                            alert('Failed to enable group.');
                        }
                    },
                    error: function() {
                        alert('An error occurred while enabling the group.');
                    }
                });
            }
        }

        function disableGroup(groupId) {
            if (confirm('Are you sure you want to disable this group?')) {
                $.ajax({
                    url: '${pageContext.request.contextPath}/RoleController/disableGroup',
                    type: 'POST',
                    data: { groupId: groupId },
                    success: function(response) {
                        if (response === 'SUCCESS') {
                            alert('Group disabled successfully!');
                            loadGroupList();
                        } else {
                            alert('Failed to disable group.');
                        }
                    },
                    error: function() {
                        alert('An error occurred while disabling the group.');
                    }
                });
            }
        }

        function deleteGroup(groupId) {
            if (confirm('Are you sure you want to delete this group? This action cannot be undone.')) {
                $.ajax({
                    url: '${pageContext.request.contextPath}/RoleController/deleteGroup',
                    type: 'POST',
                    data: { groupId: groupId },
                    success: function(response) {
                        if (response === 'SUCCESS') {
                            alert('Group deleted successfully!');
                            loadGroupList();
                        } else {
                            alert('Failed to delete group.');
                        }
                    },
                    error: function() {
                        alert('An error occurred while deleting the group.');
                    }
                });
            }
        }

        function refreshGroupList() {
            loadGroupList();
        }
    </script>
</body>
</html>
