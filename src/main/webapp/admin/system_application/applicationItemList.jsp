<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.Date" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <title>Sample JSP Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .table th, .table td {
            vertical-align: middle;
        }
    </style>
    <link href="/css/common/tableStyle.css" rel="stylesheet" type="text/css"/>

    <style>
        .section-box {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .form-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row input[type=text] {
            flex: 1;
            padding: 6px 8px;
            border: 1px solid #ccc;
            border-radius: 6px;
        }

        .form-row button:hover {
            background: #0056b3;
        }

        select[multiple] {
            width: 100%;
            height: 120px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 6px;
        }

        .transfer-container {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .transfer-list {
            width: 45%;
            height: 400px;
            border: 1px solid #ccc;
            border-radius: 8px;
            overflow-y: auto;
            padding: 10px;
            background: #f9f9f9;
        }

        .transfer-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .transfer-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .transfer-buttons button {
            padding: 8px 12px;
            border: none;
            background: #007bff;
            color: white;
            cursor: pointer;
            border-radius: 5px;
            transition: 0.2s;
        }

        .transfer-buttons button:hover {
            background: #0056b3;
        }
    </style>


</head>
<body>

<%
    // Java code block (scriptlet)
    Date now = new Date();
    int hour = now.getHours();
    String greeting;

    if (hour < 12) {
        greeting = "Good morning!";
    } else if (hour < 18) {
        greeting = "Good afternoon!";
    } else {
        greeting = "Good evening!";
    }
%>
<div class="container-fluid">
    <div class="row header-bg">
        <div class="col-sm-12 py-2 bg-dark">
            <h5 class="float-left text-dark">System User Roles</h5>
        </div>
    </div>

    <div class="row header-bg mt-2" style="justify-content: center">
        <div class="col-sm-6">
            <div class="section-box">
                <p>Group Name</p>
                <div class="form-row">
                    <input type="text" id="roleName" value="${groupName}" placeholder="Enter role name" readonly/>
                    <input type="hidden" id="groupId" value="${groupId}"/>
                    <input type="hidden" id="groupStatus" value="${userGroupDto.status}"/>
                    <button type="button" class="btn btn-danger" onclick="deleteGroup()">Delete Group</button>
                    <button type="button" id="statusToggleBtn" class="btn" onclick="toggleGroupStatus()">${userGroupDto.status == 'ACTIVE' ? 'Disable Group' : 'Enable Group'}</button>
                    <button type="button" class="btn btn-primary" onclick="updateGroup()">Update Group</button>
                </div>
            </div>

        </div>
    </div>
    <div class="row header-bg mt-2" style="justify-content: center">
        <div class="col-sm-6">
            <div class="transfer-container">
                <!-- Left List -->
                <div class="transfer-list" id="available">
                    <p>Available Roles</p>
                    <c:forEach var="role" items="${allRoles}">
                        <c:set var="isAssigned" value="false"/>
                        <c:if test="${roleDetails.assignedRoles != null}">
                            <c:forEach var="assignedRole" items="${roleDetails.assignedRoles}">
                                <c:if test="${assignedRole.roleId == role.roleId}">
                                    <c:set var="isAssigned" value="true"/>
                                </c:if>
                            </c:forEach>
                        </c:if>
                        <c:if test="${!isAssigned}">
                            <div class="transfer-item">
                                <input type="checkbox" value="${role.roleId}"> ${role.roleName}
                            </div>
                        </c:if>
                    </c:forEach>
                </div>

                <!-- Buttons -->
                <div class="transfer-buttons">
                    <button onclick="moveSelected('available','assigned')"> &gt; </button>
                    <button onclick="moveSelected('assigned','available')"> &lt; </button>
                </div>

                <!-- Right List -->
                <div class="transfer-list" id="assigned">
                    <p>Assigned Roles</p>
                    <c:if test="${roleDetails.assignedRoles != null}">
                        <c:forEach var="assignedRole" items="${roleDetails.assignedRoles}">
                            <div class="transfer-item">
                                <input type="checkbox" value="${assignedRole.roleId}"> ${assignedRole.roleName}
                            </div>
                        </c:forEach>
                    </c:if>
                </div>
            </div>
        </div>
    </div>

</div>

<script>
    var contextPath = '${pageContext.request.contextPath}';

    function updateStatusButton() {
        const status = document.getElementById("groupStatus").value;
        const btn = document.getElementById("statusToggleBtn");

        if (status === 'ACTIVE') {
            btn.textContent = 'Disable Group';
            btn.className = 'btn btn-warning';
        } else {
            btn.textContent = 'Enable Group';
            btn.className = 'btn btn-success';
        }
    }

    function toggleGroupStatus() {
        const groupId = document.getElementById("groupId").value;
        const status = document.getElementById("groupStatus").value;
        if (!groupId) {
            notify("Group ID not found.", "danger");
            return;
        }

        const isActive = status === 'ACTIVE';
        const action = isActive ? 'disable' : 'enable';
        const message = `Are you sure you want to \${action} this group?`;

        bootbox.confirm({
            message: message,
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: isActive ? 'btn-warning' : 'btn-success',
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary'
                }
            },
            callback: function (result) {
                if (!result) return;

                const url = contextPath + "/rolesController/" + (isActive ? "disableGroup" : "enableGroup");
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: { groupId: groupId },
                    success: function (result) {
                        try {
                            var messageType = result;
                            if (messageType.status == "success") {
                                notify(`User Group \${action}d successfully!`, "success");
                                document.getElementById("groupStatus").value = isActive ? 'DISABLED' : 'ACTIVE';
                                updateStatusButton();
                                setTimeout(function() {
                                    window.location = contextPath + "/rolesController/loadAllGroups";
                                }, 2000);
                            } else {
                                notify(`Failed to \${action} user Group: ` + messageType.message, "danger");
                            }
                        } catch (e) {
                            console.error("Error parsing server response:", e);
                            alert("An unexpected error occurred.");
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error("AJAX Error:", textStatus, errorThrown);
                        notify("A server error occurred.", "danger");
                    }
                });
            }
        });
    }

    // Delete group
    function deleteGroup() {
        const groupId = document.getElementById("groupId").value;
        if (!groupId) {
            notify("Group ID not found.", "danger");
            return;
        }

        bootbox.confirm({
            message: "Are you sure you want to delete this group?",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-danger'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary'
                }
            },
            callback: function (result) {
                if (result) {
                    $.ajax({
                        url: contextPath + "/rolesController/deleteGroup",
                        type: 'POST',
                        data: { groupId: groupId },
                        success: function (response) {
                            try {
                                if (response.status == "success") {
                                    notify("User Group Deleted successfully!", "success");
                                    setTimeout(function() {
                                        window.location = contextPath + "/rolesController/loadAllGroups";
                                    }, 2000);
                                } else {
                                    notify("Failed to Delete user Group: " + response.message, "danger");
                                }
                            } catch (e) {
                                console.error("Error parsing server response:", e);
                                alert("An unexpected error occurred.");
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.error("AJAX Error:", textStatus, errorThrown);
                            notify("A server error occurred. Failed to delete the user group.", "danger");
                        }
                    });
                }
            }
        });
    }

    // Update group roles
    function updateGroup() {
        const groupId = document.getElementById("groupId").value;
        if (!groupId) {
            notify("Group ID not found.", "danger");
            return;
        }

        // Get assigned role IDs
        const assignedRoles = [];
        const assignedCheckboxes = document.querySelectorAll("#assigned input[type=checkbox]");
        assignedCheckboxes.forEach(cb => {
            assignedRoles.push(cb.value);
        });

        const roleIds = assignedRoles.join(",");

        $.ajax({
            url: contextPath + "/rolesController/updateRoles",
            type: 'POST',
            contentType: 'application/json; charset=utf-8',
            data: JSON.stringify({
                groupId: parseInt(groupId),
                roleIds: roleIds
            }),
            success: function (result) {
                try {
                    var messageType = result;
                    if (messageType.status == "success") {
                        notify("User Group Roles Updated successfully!", "success");
                        setTimeout(function() {
                            window.location = contextPath + "/rolesController/loadAllGroups";
                        }, 2000);
                    } else {
                        notify("Failed to Update user Group Roles: " + messageType.message, "danger");
                    }
                } catch (e) {
                    console.error("Error parsing server response:", e);
                    alert("An unexpected error occurred.");
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error("AJAX Error:", textStatus, errorThrown);
                notify("A server error occurred. Failed to save the user group.", "danger");
            }
        });
    }


    // Transfer roles
    function moveSelected(fromId, toId) {
        const from = document.getElementById(fromId);
        const to = document.getElementById(toId);

        const checkedBoxes = from.querySelectorAll("input[type=checkbox]:checked");
        checkedBoxes.forEach(cb => {
            cb.checked = false;
            const parent = cb.parentElement;
            to.appendChild(parent);
        });
    }

    // Hide loader (your existing function call)
    hideLoader();
</script>

</body>
<script>
    updateStatusButton();
    hideLoader()
</script>




</html>
